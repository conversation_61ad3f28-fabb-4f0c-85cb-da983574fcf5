import 'dart:io';
import 'dart:math';

import 'package:flame/game.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/Navigator.dart';
import 'package:simsushare_player/components/SimPlayer.dart' if (dart.library.html) 'package:simsushare_player/components/SimPlayerWeb.dart';
import 'package:simsushare_player/components/MultiViewGrid.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/controllers/UserController.dart';
import 'package:simsushare_player/models/CloudScenario.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/NewScenarioWithPlayer.dart';
import 'package:simsushare_player/pages/NewScenarioWithPlayer/components/PlayMenu.dart';
import 'package:simsushare_player/utils/constants.dart';

class Preview extends StatelessWidget {
  final String? url;
  Preview({
    Key? key,
    this.url,
  }) : super(key: key);

  final args = Get.arguments;

  Rx<List<CloudScenario>> scenarios = Rx<List<CloudScenario>>([]);
  bool inEditor = false;

  SimPlayer sp = SimPlayer(playMode: true);
  final _simController = Get.find<SimController>();
  late Scenario sim;

  // FlutterView theView = WidgetsBinding.instance.platformDispatcher.views.first;

  _initialize() async {
    if (url != null) {
      UserController _userController = Get.find();
      if (_userController.user.value == null) {
        print("User value is null in user controller");
        return;
      }
      /* try { */
      // print(_userController.user.value);
      final response = await dio.get("/scenarios/" + _userController.user.value["company"]["_id"]);
      // print("${response.statusCode} ${response.data}");
      if (response.statusCode != 200) {
        return print("Failed to fetch scenarios: ${response.statusCode} ${response.data}");
      }
      final scenariosData = response.data["scenarios"];
      print(scenariosData[0]);
      print(scenariosData[0].runtimeType);
      print(scenariosData[0].toString());
      scenarios.value = List<CloudScenario>.from(scenariosData.map((s) => CloudScenario.fromJson(s)), growable: true);
      // print("Scenarios: ${scenarios.value}");
      /* } catch (err) {
      print(err);
    } */
    }
    // if (_simController.currentSim.value == null) return;
    sim = _simController.currentSim.value!.copy();
    sim.inPlayMode = true;
    sim.masks = sim.masks.map((mask) {
      if (!mask.needsParsing()) {
        mask.scale(1 / sim.width, 1 / sim.height);
      }
      return mask;
    }).toList();

    if (Get.arguments?["inEditor"] == "true") {
      inEditor = true;
    }
    sp = SimPlayer(playMode: true, inEditor: inEditor, providedSim: sim);
  }

  double screenWidth = (kIsWeb && Get.context!.orientation == Orientation.landscape
      ? WidgetsBinding.instance.platformDispatcher.views.first.physicalSize.height
      : WidgetsBinding.instance.platformDispatcher.views.first.physicalSize.width);
  // : Get.width);
  double screenHeight = (kIsWeb && Get.context!.orientation == Orientation.landscape
      ? WidgetsBinding.instance.platformDispatcher.views.first.physicalSize.width
      : WidgetsBinding.instance.platformDispatcher.views.first.physicalSize.height);
  // : Get.height);

  @override
  Widget build(BuildContext context) {
    // final screenWidth = (kIsWeb ? theView.physicalSize.width : Get.width);
    // final screenHeight = (kIsWeb ? theView.physicalSize.height : Get.height);
    // print("isWeb: $kIsWeb, Screen width: $screenWidth, Screen height: $screenHeight");
    if (!kIsWeb) {
      if (Platform.isAndroid || Platform.isIOS) /* (breakpoint == Breakpoints.small) */ {
        SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft]);
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
        final tempWidth = screenWidth;
        screenWidth = screenHeight;
        screenHeight = tempWidth;
      }
    }
    _initialize();
    // final selectedWidth = min(1200.0, sim.width);
    // final selectedHeight = min(800.0, sim.height);
    // final selectedWidth = min(1200.0, screenWidth);
    // final selectedHeight = min(800.0, screenHeight);
    // final selectedScale = min(1200 / selectedWidth, 800 / selectedHeight);
    // print("======= Preview ======== $selectedWidth x $selectedHeight @ (${1200 / selectedWidth} vs ${800 / selectedHeight}) $selectedScale");
    final selectedWidth = min(sim.width, screenWidth);
    final selectedHeight = min(sim.height, screenHeight);
    final selectedScale = min(sim.width / selectedWidth, sim.height / selectedHeight);
    print(
        "======= Preview ======== $selectedWidth x $selectedHeight @ (${sim.width / selectedWidth} vs ${sim.height / selectedHeight}) $selectedScale");
    print("SIM DIMENSIONS: ${sim.width} x ${sim.height}");
    return Scaffold(
      body: Container(
        // SafeArea(
        child: SizedBox.expand(
          child: KeyboardListener(
            child: ClipRRect(
              clipBehavior: Clip.hardEdge,
              child: Stack(
                children: [
                  Center(
                    child: SizedBox(
                      /* decoration: BoxDecoration(
                        border: Border.all(color: Colors.pink, width: 2),
                      ), */
                      // width: 1200,
                      // height: 800,
                      width: selectedWidth * selectedScale,
                      height: selectedHeight * selectedScale,
                      // width: sim.width,
                      // height: sim.height,
                      child: Obx(() {
                        if (_simController.isMultiViewMode.value) {
                          final multiViewLocations = _simController.getMultiViewLocations();
                          if (multiViewLocations.isNotEmpty) {
                            return MultiViewGrid(
                              locations: multiViewLocations,
                              gridType: _simController.multiViewGridType.value,
                              sim: sim,
                            );
                          }
                        }
                        // Default single view
                        return Center(
                          child: GameWidget(game: sp),
                        );
                      }),
                    ),
                  ),
                  // Only show navigator in single view mode
                  if (!_simController.isMultiViewMode.value)
                    Obx(
                      (() {
                        try {
                          final widthFactor = screenWidth / (_simController.currentSim.value?.width ?? 1);
                          final heightFactor = screenHeight / (_simController.currentSim.value?.height ?? 1);
                          print("Width factor: $widthFactor, Height factor: $heightFactor");
                          print("Screen width: $screenWidth, Screen height: $screenHeight");
                          print("Sim width: ${_simController.currentSim.value?.width}, Sim height: ${_simController.currentSim.value?.height}");
                          print("Calculation width: $screenWidth / ${_simController.currentSim.value?.width ?? 1}");
                          print("Calculation height: $screenHeight / ${_simController.currentSim.value?.height ?? 1}");
                          // NOTE: For some reason, a bias of 36 multiplied by the factor is required to get the correct position
                          return Positioned(
                            left: _simController.currentSim.value?.navClusterX != null
                                ? (_simController.currentSim.value!.navClusterX! * widthFactor) + (36 * widthFactor) /* bias */
                                : 20,
                            bottom: _simController.currentSim.value?.navClusterY != null
                                ? (_simController.currentSim.value!.navClusterY! * heightFactor) + (36 * heightFactor) /* bias */
                                : 20,
                            // height: isMobileScreen || isMobile ? 160 : screenHeight * 0.2,
                            // width: isMobileScreen || isMobile ? 160 : screenHeight * 0.2,
                            height: isMobileScreen || isMobile ? selectedHeight : screenHeight * 0.2,
                            width: isMobileScreen || isMobile ? selectedWidth : screenHeight * 0.2,
                            child: SimNavigator(locId: sim.locations[_simController.currentLocation.value].id, scenario: sim),
                          );
                        } catch (err, stacktrace) {
                          print("$err+${stacktrace.toString()}");
                          return Text(_simController.currentSim.value == null ? "No current sim" : "Error");
                        }
                      }),
                    ),
                  if (!kIsWeb)
                    Positioned(
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: const BoxDecoration(
                          color: mainBackgrounds,
                          borderRadius: BorderRadius.only(bottomRight: Radius.circular(10)),
                        ),
                        child: IconButton(
                          onPressed: () {
                            _simController.currentSim.value!.inPlayMode = false;
                            _simController.currentSim.refresh();
                            if (inEditor) {
                              // Get.offAndToNamed("/create", parameters: );
                              // Get.offNamed("/create");
                              Get.off(NewScenarioWithPlayer(), duration: const Duration(milliseconds: 300));
                            } else {
                              Get.back();
                            }
                            // Get.offAllNamed("/home");
                            // Get.back();
                            /* Future.delayed(const Duration(milliseconds: 500), () {
                              _simController.currentSim.refresh();
                            }); */
                          },
                          icon: const Icon(
                            Icons.arrow_back_ios,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  // Only show location dropdown in single view mode
                  if (!_simController.isMultiViewMode.value)
                    Obx(
                      () => Positioned(
                        top: 10,
                        left: 70,
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 10),
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                            // border: Border.all(color: mainBackgrounds),
                          ),
                          constraints: const BoxConstraints(maxWidth: 150),
                          child: DropdownButton(
                            isExpanded: true,
                            isDense: true,
                            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 0),
                            value: sim.locations[_simController.currentLocation.value].id,
                            onChanged: (value) {
                              _simController.currentLocation.value = sim.locations.indexWhere((loc) => loc.id == value);
                              _simController.currentSim.refresh();
                            },
                            underline: const SizedBox(),
                            style: const TextStyle(color: Colors.white, fontSize: 20),
                            dropdownColor: Colors.white,
                            borderRadius: const BorderRadius.all(Radius.circular(10)),
                            items: sim.locations
                                .map(
                                  (loc) => DropdownMenuItem(
                                    value: loc.id,
                                    child: Text(
                                      "${loc.name} (${sim.states.firstWhere((state) => loc.state == state.id).name})",
                                      style: const TextStyle(color: Colors.black, fontSize: 14),
                                    ),
                                  ),
                                )
                                .toList(),
                          ),
                        ),
                      ),
                    ),
                  // Only show state dropdown in single view mode
                  if (!_simController.isMultiViewMode.value)
                    Obx(
                      () => Positioned(
                        top: 10,
                        left: 230,
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 10),
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                            // border: Border.all(color: mainBackgrounds),
                          ),
                          constraints: const BoxConstraints(maxWidth: 150),
                          child: DropdownButton(
                            isExpanded: true,
                            isDense: true,
                            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 0),
                            value: sim.locations[_simController.currentLocation.value].state,
                            onChanged: (value) {
                              final targetState = sim.states.indexWhere((state) => state.id == value);
                              final targetLocation = sim.locations.firstWhereOrNull(
                                  (loc) => loc.state == value && loc.name == sim.locations[_simController.currentLocation.value].name);
                              if (targetLocation != null) {
                                _simController.currentLocation.value = sim.locations.indexWhere((loc) => loc.id == targetLocation.id);
                              } else {
                                _simController.currentLocation.value = sim.locations.indexWhere((loc) => loc.state == value);
                              }
                              _simController.currentState.value = targetState;
                              _simController.currentSim.refresh();
                            },
                            underline: const SizedBox(),
                            style: const TextStyle(color: Colors.white, fontSize: 20),
                            dropdownColor: Colors.white,
                            borderRadius: const BorderRadius.all(Radius.circular(10)),
                            items: sim.states
                                .map(
                                  (state) => DropdownMenuItem(
                                    value: state.id,
                                    child: Text(
                                      state.name,
                                      style: const TextStyle(color: Colors.black, fontSize: 14),
                                    ),
                                  ),
                                )
                                .toList(),
                          ),
                        ),
                      ),
                    ),
                  const HideNavigatorButton(),
                  // Multi-view indicator
                  Obx(() {
                    if (_simController.isMultiViewMode.value) {
                      return Positioned(
                        top: 10,
                        left: 70,
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                          decoration: BoxDecoration(
                            color: yellow.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: yellow, width: 2),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.view_module_outlined,
                                color: Colors.black,
                                size: 16,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                'Multi-View (${_simController.multiViewGridType.value} locations)',
                                style: const TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  }),
                  PlayMenu(),
                ],
              ),
            ),
            onKeyEvent: (event) {
              final _simController = Get.find<SimController>();
              final sim = _simController.currentSim.value!;
              final currentLocation = sim.locations[_simController.currentLocation.value];
              int index = -1;
              switch (event.logicalKey.keyLabel.toLowerCase()) {
                case "w":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "N");
                  break;
                case "a":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "W");
                  break;
                case "d":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "E");
                  break;
                case "x":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "S");
                  break;
                case "q":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "NW");
                  break;
                case "e":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "NE");
                  break;
                case "z":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "SW");
                  break;
                case "c":
                  index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "SE");
                  break;
                default:
                  if (event is KeyDownEvent && event.logicalKey == LogicalKeyboardKey.arrowUp) {
                    index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "UP");
                    break;
                  }
                  if (event is KeyDownEvent && event.logicalKey == LogicalKeyboardKey.arrowDown) {
                    index = sim.navigations.indexWhere((nav) => nav.from == currentLocation.id && nav.direction == "DOWN");
                  }
              }
              if (index != -1) {
                _simController.currentLocation.value = sim.locations.indexWhere((loc) => loc.id == sim.navigations[index].to);
                _simController.currentSim.refresh();
              }
            },
            focusNode: FocusNode(),
            autofocus: true,
          ),
        ),
      ),
    );
  }
}
