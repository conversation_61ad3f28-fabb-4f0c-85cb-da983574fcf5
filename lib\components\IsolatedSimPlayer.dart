import 'dart:async' as asynch;
import 'dart:math';
import 'dart:ui' as ui;
import 'package:collection/collection.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flame/cache.dart';
import 'package:flame/components.dart';
import 'package:flame/events.dart';
import 'package:flame/flame.dart';
import 'package:flame/game.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:nanoid/nanoid.dart';
import 'package:simsushare_player/components/SimPlayer.dart' if (dart.library.html) 'package:simsushare_player/components/SimPlayerWeb.dart';
import 'package:simsushare_player/controllers/ClipboardController.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/utils/constants.dart';
// import 'package:simsushare_player/utils/extensions.dart'; // File not found
import 'package:flame/rendering.dart' as frendering;

/// An isolated SimPlayer that displays a specific location without depending on global SimController state
class IsolatedSimPlayer extends FlameGame {
  final Scenario sim;
  final String locationId;
  final bool playMode;

  late int currentLocationIndex;
  late SimulationLocation currentLocation;
  late double initialSize;
  late ui.Image? bgImage;
  List<SimSpriteAnimationComponent> sprites = <SimSpriteAnimationComponent>[];
  String id = nanoid(10);
  List<String> hiddenImages = <String>[];
  List<String> hiddenPeople = <String>[];
  final instanceStream = asynch.StreamController<dynamic>.broadcast();

  // Timer for play mode
  late asynch.Timer _timer;
  int simTime = 0;
  int locationTime = 0;
  int stateTime = 0;

  // Image caches
  final peopleCache = Images(prefix: "assets/people/");
  final containersCache = Images(prefix: "assets/containers/");
  Map<String, ui.Image> peopleImages = <String, ui.Image>{};
  Map<String, ui.Image> containersImages = <String, ui.Image>{};

  // Icons
  late ui.Image expandIcon;
  late ui.Image rotateIcon;
  late ui.Image volumeIcon;
  late ui.Image removeIcon;
  late ui.Image playPauseIcon;

  // Play mode compensator for scaling
  Vector2 playModeCompensator = Vector2(1, 1);

  IsolatedSimPlayer({
    required this.sim,
    required this.locationId,
    this.playMode = true,
  });

  @override
  Future<void> onLoad() async {
    print("IsolatedSimPlayer ID: $id, playMode: $playMode, locationId: $locationId");

    // Find the location index
    currentLocationIndex = sim.locations.indexWhere((loc) => loc.id == locationId);
    if (currentLocationIndex == -1) {
      print("Location with ID $locationId not found!");
      return;
    }

    currentLocation = sim.locations[currentLocationIndex];
    print("Found location: ${currentLocation.name} at index $currentLocationIndex");

    initialSize = getInitialSize();

    await super.onLoad();

    // Set up camera for play mode
    if (playMode) {
      camera = CameraComponent.withFixedResolution(width: sim.width, height: sim.height);
      playModeCompensator = Vector2(1, 1);
    } else {
      double intendedWidth = size.x;
      double intendedHeight = size.x * 8 / 12;
      if (intendedHeight > size.y) {
        intendedHeight = size.y;
        intendedWidth = size.y * 12 / 8;
      }
      camera = CameraComponent.withFixedResolution(width: intendedWidth, height: intendedHeight);
      playModeCompensator = Vector2(intendedWidth / 1200, intendedHeight / 800);
    }

    // Initialize hidden lists for play mode
    if (playMode) {
      for (final img in currentLocation.images) {
        if (img.hideOnStart) {
          hiddenImages.add(img.id);
        }
      }
      for (final person in currentLocation.people) {
        if (person.hideOnStart) {
          hiddenPeople.add(person.id);
        }
      }

      // Start the timer for play mode
      _timer = asynch.Timer.periodic(const Duration(seconds: 1), (t) {
        if (!playMode) {
          _timer.cancel();
        }
        const secs = 1;
        simTime += secs;
        locationTime += secs;
        stateTime += secs;

        // Handle timed jumpers
        final jumperTimed = currentLocation.jumpers.firstWhereOrNull((jmp) => jmp.delay > 0 && jmp.delay <= locationTime);
        if (jumperTimed != null && sim.inPlayMode) {
          // For isolated player, we don't jump to other locations
          print("Timed jumper found but isolated player doesn't support location jumping");
        }

        // Handle timed state changes, image shows/hides, etc.
        _handleTimedEvents();
      });
    }

    // Load icons
    expandIcon = await Flame.images.load("expand.png");
    rotateIcon = await Flame.images.load("rotate-arrow.png");
    volumeIcon = await Flame.images.load("volume.png");
    removeIcon = await Flame.images.load("remove.png");
    playPauseIcon = await Flame.images.load("pause_filled.png");

    // Load people images
    for (var curr in peoplePostureCount.keys) {
      final count = peoplePostureCount[curr]!;
      for (var i = 0; i < count; i++) {
        peopleImages["$curr-$i"] = await peopleCache.load("$curr/$i.png", key: "$curr-$i");
      }
    }

    // Load container images
    for (var curr in containerViewsMapping.keys) {
      final count = containerViewsMapping[curr]!.length;
      for (var i = 0; i < count; i++) {
        containersImages["$curr-$i"] = await containersCache.load("${containerAssetsMapping[curr]}/$i.png", key: "$curr-$i");
      }
    }

    await buildBgCache();
    await redraw();
  }

  double getInitialSize() {
    return playMode ? size.x / 8 : size.x / 8;
  }

  Future buildBgCache({clearCache = false}) async {
    paused = true;
    if (clearCache) {
      Flame.images.clear("background-$id-$currentLocationIndex");
    }

    // Load background image for current location
    if (currentLocation.image.isNotEmpty) {
      try {
        if (kIsWeb) {
          await Flame.images.fromBase64("background-$id-$currentLocationIndex", currentLocation.image);
        } else {
          // For mobile, assume it's a file path
          bgImage = await Flame.images.load(currentLocation.image, key: "background-$id-$currentLocationIndex");
        }
      } catch (e) {
        print("Error loading background image: $e");
      }
    }

    paused = false;
  }

  void _handleTimedEvents() {
    // Handle timed image shows/hides
    for (final img in currentLocation.images) {
      if (img.showAfter > 0 && img.showAfter <= locationTime && hiddenImages.contains(img.id)) {
        hiddenImages.remove(img.id);
        redraw();
      }
      if (img.hideAfter > 0 && img.hideAfter <= locationTime && !hiddenImages.contains(img.id)) {
        hiddenImages.add(img.id);
        redraw();
      }
    }

    // Handle timed people shows/hides
    for (final person in currentLocation.people) {
      if (person.showAfter > 0 && person.showAfter <= locationTime && hiddenPeople.contains(person.id)) {
        hiddenPeople.remove(person.id);
        redraw();
      }
      if (person.hideAfter > 0 && person.hideAfter <= locationTime && !hiddenPeople.contains(person.id)) {
        hiddenPeople.add(person.id);
        redraw();
      }
    }
  }

  Future<void> redraw() async {
    if (sim == null) {
      print("Null sim in isolated game. Returning");
      return;
    }

    print("IsolatedSimPlayer redraw - Size: $size");
    print("Location: ${currentLocation.name}, Image scale: ${currentLocation.imageScale}");
    print("Sim Dimensions: ${sim.width} x ${sim.height}");

    removeAll(children);

    // Render background
    ui.Image? bg;
    if (Flame.images.containsKey("background-$id-$currentLocationIndex")) {
      bg = Flame.images.fromCache("background-$id-$currentLocationIndex");
    }

    if (bg != null) {
      final brightness = currentLocation.imageBrightness;
      final bgComponent = SpriteComponent()
        ..sprite = Sprite(bg)
        ..size = Vector2(
          bg.width.toDouble() * currentLocation.imageScale * playModeCompensator.x,
          bg.height.toDouble() * currentLocation.imageScale * playModeCompensator.y,
        )
        ..angle = currentLocation.imageRotation * pi / 180
        ..anchor = Anchor.center
        ..position = Vector2(size.x / 2, size.y / 2)
        ..decorator.addLast(
          frendering.PaintDecorator.tint(
            brightness < 0 ? Colors.black.withOpacity(brightness / -20) : Colors.white.withOpacity(brightness / 20),
          ),
        )
        ..priority = 0;

      add(bgComponent);
    } else {
      // Add colored background if no image
      final colorComponent = RectangleComponent()
        ..size = size
        ..paint = (Paint()..color = Color(int.parse(currentLocation.color.replaceFirst('#', '0xFF'))))
        ..priority = 0;
      add(colorComponent);
    }

    // Render sprites
    await _renderSprites();

    // Render images
    await _renderImages();

    // Render people
    await _renderPeople();

    // Render texts
    await _renderTexts();

    // Render shapes
    await _renderShapes();

    // Render containers
    await _renderContainers();

    // Render jumpers (if not in play mode)
    if (!playMode) {
      await _renderJumpers();
    }

    // Render sounds (if not in play mode)
    if (!playMode) {
      await _renderSounds();
    }

    // Render labels
    await _renderLabels();

    // Render timers
    await _renderTimers();
  }

  Future<void> _renderSprites() async {
    for (int index = 0; index < currentLocation.sprites.length; index++) {
      final sprite = currentLocation.sprites[index];
      if (sprite.hidden) continue;

      try {
        // Use the sprite's img property directly since it's already loaded
        final spriteFrames = sprite.frames;
        if (spriteFrames.isEmpty) continue;

        double largestWidth = 0;
        double largestHeight = 0;
        final scaleDiffWidth = sprite.img.width / sprite.width;
        final scaleDiffHeight = sprite.img.height / sprite.height;

        // Create animation from the sprite frames using SpriteAnimationFrameData
        final animData = SpriteAnimationData(
          spriteFrames.map((frame) {
            if (frame.width > largestWidth) largestWidth = frame.width;
            if (frame.height > largestHeight) largestHeight = frame.height;
            return SpriteAnimationFrameData(
              srcPosition: Vector2(frame.x * scaleDiffWidth, frame.y * scaleDiffHeight),
              srcSize: Vector2(frame.width * scaleDiffWidth, frame.height * scaleDiffHeight),
              stepTime: sprite.speed,
            );
          }).toList(),
        );

        final anim = SpriteAnimation.fromFrameData(sprite.img, animData);

        final mainWidth = largestWidth * sprite.scale * sprite.widthScale;
        final mainHeight = largestHeight * sprite.scale * sprite.heightScale;

        final spriteComponent = SimSpriteAnimationComponent(
          playMode: playMode && !sprite.movable,
          onDragged: (comp) {
            // Handle drag if needed
          },
          onTapped: (comp) {
            // Handle tap if needed
          },
          simSize: Vector2(sim.width, sim.height),
          masks: [], // Add masks if needed
        )
          ..animation = anim
          ..position = Vector2(size.x * sprite.x, size.y * sprite.y)
          ..angle = sprite.rotation * pi / 180
          ..size = Vector2(mainWidth * playModeCompensator.x, mainHeight * playModeCompensator.y)
          ..anchor = Anchor.center
          ..tint(sprite.filterColor)
          ..setOpacity(sprite.opacity)
          ..decorator.addLast(frendering.PaintDecorator.blur(sprite.blur))
          ..priority = sprite.priority + 1;

        add(spriteComponent);
      } catch (e) {
        print("Error loading sprite ${sprite.assetName}: $e");
      }
    }
  }

  Future<void> _renderImages() async {
    for (int index = 0; index < currentLocation.images.length; index++) {
      final img = currentLocation.images[index];
      if (img.hidden) continue;

      try {
        // SimImage uses img property for the actual image and path for the file path
        final ic = SimSpriteComponent(
          playMode: playMode && !img.movable,
          onDragged: (comp, event) {
            if (playMode && !img.movable) return;
            comp.position += event.delta;
          },
          onTapped: (comp, event) {
            // Handle tap if needed
          },
          masks: [], // Add masks if needed
        )
          ..sprite = Sprite(img.img)
          ..position = Vector2(img.x * size.x / sim.width, img.y * size.y / sim.height)
          ..angle = img.rotation * pi / 180
          ..anchor = Anchor.center
          ..size = Vector2(
            img.width * img.scale * img.widthScale * playModeCompensator.x,
            img.height * img.scale * img.heightScale * playModeCompensator.y,
          )
          ..opacity = hiddenImages.contains(img.id) ? 0 : img.opacity
          ..decorator.addLast(frendering.PaintDecorator.blur(img.blur))
          ..decorator.addLast(frendering.PaintDecorator.tint(img.filterColor))
          ..priority = img.priority + 1;

        if (img.mirrorX) {
          ic.flipVertically();
        }
        if (img.mirrorY) {
          ic.flipHorizontally();
        }

        add(ic);
      } catch (e) {
        print("Error loading image ${img.path}: $e");
      }
    }
  }

  Future<void> _renderPeople() async {
    for (int index = 0; index < currentLocation.people.length; index++) {
      final person = currentLocation.people[index];
      if (person.hidden) continue;

      final personImage = peopleImages["${peopleToAssetMapping[person.type]}-${person.posture}"];
      if (personImage == null) continue;

      final initialSize = personImage.width >= personImage.height
          ? clampDouble(personImage.width.toDouble(), size.x / 6, size.x / 3)
          : clampDouble(personImage.height.toDouble(), size.y / 6, size.y / 3);

      final pc = SimSpriteComponent(
        playMode: playMode && !person.movable,
        onDragged: (comp, event) {
          if (playMode && !person.movable) return;
          comp.position += event.delta;
        },
        onTapped: (comp, event) {
          // Handle tap if needed
        },
        masks: [], // Add masks if needed
      )
        ..sprite = Sprite(personImage)
        ..position = Vector2(person.x * size.x / sim.width, person.y * size.y / sim.height)
        ..angle = person.rotation * pi / 180
        ..anchor = Anchor.center
        ..size = Vector2(initialSize * person.scale * person.widthScale,
            initialSize * (personImage.height / personImage.width) * person.scale * person.heightScale)
        ..opacity = hiddenPeople.contains(person.id) ? 0 : person.opacity
        ..decorator.addLast(frendering.PaintDecorator.blur(person.blur))
        ..decorator.addLast(frendering.PaintDecorator.tint(person.filterColor))
        ..scale = Vector2(person.scale, person.scale)
        ..priority = person.priority + 1;

      add(pc);
    }
  }

  Future<void> _renderTexts() async {
    for (int index = 0; index < currentLocation.texts.length; index++) {
      final text = currentLocation.texts[index];
      if (text.hidden) continue;

      final textComponent = TextComponent(
        text: text.text,
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: 16 * playModeCompensator.x, // Default font size since SimText doesn't have fontSize
            color: text.filterColor, // SimText uses filterColor for text color
            fontWeight: FontWeight.normal, // Default weight
            fontStyle: FontStyle.normal, // Default style
          ),
        ),
      )
        ..position = Vector2(text.x * size.x / sim.width, text.y * size.y / sim.height)
        ..angle = text.rotation * pi / 180
        ..anchor = Anchor.center
        ..priority = text.priority + 1;

      add(textComponent);
    }
  }

  Future<void> _renderShapes() async {
    for (int index = 0; index < currentLocation.shapes.length; index++) {
      final shape = currentLocation.shapes[index];
      if (shape.hidden) continue;

      final shapeComponent = SimShapeComponent(
        playMode: playMode && !shape.movable,
        onDragged: (comp, _) {
          if (playMode && !shape.movable) return;
          // Handle drag if needed
        },
        onTapped: (comp) {
          // Handle tap if needed
        },
        shapeObject: shape,
        masks: [], // Add masks if needed
      )
        ..position = Vector2(shape.x * size.x / sim.width, shape.y * size.y / sim.height)
        ..angle = shape.rotation * pi / 180
        ..anchor = Anchor.center
        ..size = Vector2(shape.width * shape.scale * shape.widthScale * playModeCompensator.x,
            shape.height * shape.scale * shape.heightScale * playModeCompensator.y)
        ..priority = shape.priority + 1;

      add(shapeComponent);
    }
  }

  Future<void> _renderContainers() async {
    for (int index = 0; index < currentLocation.containers.length; index++) {
      final container = currentLocation.containers[index];
      if (container.hidden) continue;

      final containerImage = containersImages["${container.type}-${container.view}"];
      if (containerImage == null) continue;

      final initialSize = Vector2(containerImage.width.toDouble(), containerImage.height.toDouble());

      final cc = SimSpriteComponent(
        playMode: playMode && !container.movable,
        onDragged: (comp, event) {
          if (playMode && !container.movable) return;
          comp.position += event.delta;
        },
        onTapped: (comp) {
          // Handle tap if needed
        },
        masks: [], // Add masks if needed
      )
        ..sprite = Sprite(containerImage)
        ..position = Vector2(container.x * size.x / sim.width, container.y * size.y / sim.height)
        ..size = Vector2(initialSize.x * container.widthScale * container.scale * playModeCompensator.x,
            initialSize.y * container.heightScale * container.scale * playModeCompensator.y)
        ..scale = Vector2(1, 1)
        ..angle = container.rotation * pi / 180
        ..anchor = Anchor.center
        ..opacity = hiddenImages.contains(container.id) ? 0 : container.opacity
        ..priority = container.priority + 1;

      add(cc);
    }
  }

  Future<void> _renderJumpers() async {
    // Jumpers are only rendered in editor mode
    if (playMode) return;

    for (int index = 0; index < currentLocation.jumpers.length; index++) {
      final jumper = currentLocation.jumpers[index];
      if (jumper.hidden) continue;

      // Render jumper as a simple rectangle for now
      final jumperComponent = RectangleComponent()
        ..position = Vector2(jumper.x * size.x / sim.width, jumper.y * size.y / sim.height)
        ..size = Vector2(50 * playModeCompensator.x, 50 * playModeCompensator.y)
        ..anchor = Anchor.center
        ..paint = Paint()
        ..color = Colors.blue.withOpacity(0.5)
        ..priority = jumper.priority + 1;

      add(jumperComponent);
    }
  }

  Future<void> _renderSounds() async {
    // Sounds are only rendered in editor mode
    if (playMode) return;

    for (int index = 0; index < currentLocation.sounds.length; index++) {
      final sound = currentLocation.sounds[index];
      if (sound.hidden) continue;

      // Render sound as a simple circle for now
      final soundComponent = CircleComponent(
        radius: 25 * playModeCompensator.x,
        paint: Paint()..color = Colors.green.withOpacity(0.5),
      )
        ..position = Vector2(sound.x * size.x / sim.width, sound.y * size.y / sim.height)
        ..anchor = Anchor.center
        ..priority = sound.priority + 1;

      add(soundComponent);
    }
  }

  Future<void> _renderLabels() async {
    for (int index = 0; index < currentLocation.labels.length; index++) {
      final label = currentLocation.labels[index];
      if (label.hidden) continue;

      final labelComponent = TextComponent(
        text: label.text,
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: label.size * playModeCompensator.x,
            color: label.filterColor,
            fontWeight: FontWeight.normal,
          ),
        ),
      )
        ..position = Vector2(label.x * size.x / sim.width, label.y * size.y / sim.height)
        ..angle = label.rotation * pi / 180
        ..anchor = Anchor.center
        ..priority = label.priority + 1;

      add(labelComponent);
    }
  }

  Future<void> _renderTimers() async {
    for (int index = 0; index < currentLocation.timers.length; index++) {
      final timer = currentLocation.timers[index];
      if (timer.hidden) continue;

      // Render timer as text showing remaining time
      final remainingTime = max(0, timer.duration - locationTime);
      final timerComponent = TextComponent(
        text: "${remainingTime}s",
        textRenderer: TextPaint(
          style: TextStyle(
            fontSize: 16 * playModeCompensator.x,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      )
        ..position = Vector2(timer.x * size.x / sim.width, timer.y * size.y / sim.height)
        ..anchor = Anchor.center
        ..priority = timer.priority + 1;

      add(timerComponent);
    }
  }

  @override
  void onDetach() {
    if (playMode && _timer.isActive) {
      _timer.cancel();
    }
    instanceStream.close();
    super.onDetach();
  }
}
