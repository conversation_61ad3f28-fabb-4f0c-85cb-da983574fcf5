import 'package:flame/game.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/components/SimPlayer.dart' if (dart.library.html) 'package:simsushare_player/components/SimPlayerWeb.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/models/Simulation.dart';

/// A specialized SimPlayer that displays a specific location independently
/// of the global SimController state
class MultiViewSimPlayer extends StatefulWidget {
  final SimulationLocation location;
  final Scenario sim;

  const MultiViewSimPlayer({
    Key? key,
    required this.location,
    required this.sim,
  }) : super(key: key);

  @override
  State<MultiViewSimPlayer> createState() => _MultiViewSimPlayerState();
}

class _MultiViewSimPlayerState extends State<MultiViewSimPlayer> {
  late SimPlayer player;
  late Scenario locationSim;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    // Create a copy of the simulation
    locationSim = widget.sim.copy(clone: true);
    locationSim.inPlayMode = true;

    // Find the target location index
    final locationIndex = locationSim.locations.indexWhere((loc) => loc.id == widget.location.id);
    
    if (locationIndex != -1) {
      // Create the player with the simulation copy
      player = SimPlayer(
        playMode: true,
        inEditor: false,
        providedSim: locationSim,
      );

      // Set up the player to show the specific location
      // We'll do this by temporarily modifying the current location in the simulation
      // This is a workaround since SimPlayer uses the global controller
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _setPlayerLocation(locationIndex);
      });
    }
  }

  void _setPlayerLocation(int locationIndex) {
    // This is a workaround to set the specific location for this player
    // In a more robust implementation, each player would have its own controller
    try {
      final simController = Get.find<SimController>();
      
      // Temporarily store the current state
      final originalLocation = simController.currentLocation.value;
      final originalSim = simController.currentSim.value;
      
      // Set our simulation and location
      simController.currentSim.value = locationSim;
      simController.currentLocation.value = locationIndex;
      
      // Force a refresh to update the player
      simController.currentSim.refresh();
      
      // Restore the original state after a brief delay
      Future.delayed(const Duration(milliseconds: 50), () {
        if (mounted) {
          simController.currentSim.value = originalSim;
          simController.currentLocation.value = originalLocation;
        }
      });
    } catch (e) {
      print('Error setting player location: $e');
    }
  }

  @override
  void dispose() {
    player.onDetach();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GameWidget(game: player);
  }
}
