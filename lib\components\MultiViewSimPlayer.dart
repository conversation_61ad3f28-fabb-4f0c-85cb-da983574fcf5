import 'package:flame/game.dart';
import 'package:flutter/material.dart';
import 'package:simsushare_player/components/IsolatedSimPlayer.dart';
import 'package:simsushare_player/models/Simulation.dart';

/// A specialized SimPlayer that displays a specific location independently
/// of the global SimController state
class MultiViewSimPlayer extends StatefulWidget {
  final SimulationLocation location;
  final Scenario sim;

  const MultiViewSimPlayer({
    Key? key,
    required this.location,
    required this.sim,
  }) : super(key: key);

  @override
  State<MultiViewSimPlayer> createState() => _MultiViewSimPlayerState();
}

class _MultiViewSimPlayerState extends State<MultiViewSimPlayer> {
  late IsolatedSimPlayer player;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    // Create an isolated player that doesn't depend on global controller
    player = IsolatedSimPlayer(
      sim: widget.sim,
      locationId: widget.location.id,
      playMode: true,
    );
  }

  @override
  void dispose() {
    player.onDetach();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GameWidget(game: player);
  }
}
