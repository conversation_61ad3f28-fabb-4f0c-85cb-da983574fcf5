import 'package:flame/game.dart';
import 'package:flutter/material.dart';
import 'package:simsushare_player/components/SimPlayer.dart' if (dart.library.html) 'package:simsushare_player/components/SimPlayerWeb.dart';
import 'package:simsushare_player/models/Simulation.dart';

/// A specialized SimPlayer that displays a specific location independently
/// of the global SimController state
class MultiViewSimPlayer extends StatefulWidget {
  final SimulationLocation location;
  final Scenario sim;

  const MultiViewSimPlayer({
    Key? key,
    required this.location,
    required this.sim,
  }) : super(key: key);

  @override
  State<MultiViewSimPlayer> createState() => _MultiViewSimPlayerState();
}

class _MultiViewSimPlayerState extends State<MultiViewSimPlayer> {
  late SimPlayer player;
  late Scenario locationSim;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    // Create a new simulation with only the target location
    final targetLocation = widget.sim.locations.firstWhere((loc) => loc.id == widget.location.id);

    locationSim = Scenario(
      name: widget.sim.name,
      locations: [targetLocation],
      states: widget.sim.states,
      navigations: widget.sim.navigations,
      masks: widget.sim.masks,
      width: widget.sim.width,
      height: widget.sim.height,
      inPlayMode: true,
    );

    // Create the player with the single-location simulation
    player = SimPlayer(
      playMode: true,
      inEditor: false,
      providedSim: locationSim,
    );
  }

  @override
  void dispose() {
    player.onDetach();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GameWidget(game: player);
  }
}
