import 'package:flutter/material.dart';
import 'package:simsushare_player/components/MultiViewSimPlayer.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/utils/constants.dart';

class MultiViewGrid extends StatefulWidget {
  final List<SimulationLocation> locations;
  final int gridType;
  final Scenario sim;

  const MultiViewGrid({
    Key? key,
    required this.locations,
    required this.gridType,
    required this.sim,
  }) : super(key: key);

  @override
  State<MultiViewGrid> createState() => _MultiViewGridState();
}

class _MultiViewGridState extends State<MultiViewGrid> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget _buildGridCell(int index) {
    if (index >= widget.locations.length) return Container();

    final location = widget.locations[index];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          // Location name header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
            decoration: BoxDecoration(
              color: mainBackgrounds.withOpacity(0.8),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
            child: Text(
              location.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          // MultiViewSimPlayer game view
          Expanded(
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(4),
                bottomRight: Radius.circular(4),
              ),
              child: MultiViewSimPlayer(
                location: location,
                sim: widget.sim,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.gridType == 2) {
      // 2x1 grid (side by side)
      return Row(
        children: [
          Expanded(child: _buildGridCell(0)),
          const SizedBox(width: 4),
          Expanded(child: _buildGridCell(1)),
        ],
      );
    } else if (widget.gridType == 4) {
      // 2x2 grid
      return Column(
        children: [
          Expanded(
            child: Row(
              children: [
                Expanded(child: _buildGridCell(0)),
                const SizedBox(width: 4),
                Expanded(child: _buildGridCell(1)),
              ],
            ),
          ),
          const SizedBox(height: 4),
          Expanded(
            child: Row(
              children: [
                Expanded(child: _buildGridCell(2)),
                const SizedBox(width: 4),
                Expanded(child: _buildGridCell(3)),
              ],
            ),
          ),
        ],
      );
    }

    return Container(); // Fallback
  }
}
