import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/components/MultiViewLocationSelector.dart';

class PlayMenu extends StatelessWidget {
  PlayMenu({Key? key}) : super(key: key);

  final hideMenu = false.obs;

  static const marginSize = 10.0;

  void _showMultiViewSelector(SimController simController) {
    if (simController.currentSim.value == null) return;

    showDialog(
      context: Get.context!,
      builder: (context) => MultiViewLocationSelector(
        availableLocations: simController.currentSim.value!.locations,
        onLocationsSelected: (locationIds, gridType) {
          simController.enableMultiView(locationIds, gridType);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final _simController = Get.find<SimController>();

    return Positioned(
      child: Obx(() => PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                child: Row(children: [Icon(Icons.play_arrow_outlined), SizedBox(width: marginSize), Text("Play")]),
                value: "play",
              ),
              const PopupMenuItem(
                child: Row(children: [Icon(Icons.pause_outlined), SizedBox(width: marginSize), Text("Pause")]),
                value: "pause",
              ),
              const PopupMenuItem(
                child: Row(children: [Icon(Icons.restart_alt_outlined), SizedBox(width: marginSize), Text("Restart")]),
                value: "restart",
              ),
              // Multi View option - only show if we have multiple locations
              if (_simController.currentSim.value != null && _simController.currentSim.value!.locations.length >= 2)
                _simController.isMultiViewMode.value
                    ? const PopupMenuItem(
                        child: Row(children: [Icon(Icons.view_agenda_outlined), SizedBox(width: marginSize), Text("Single View")]),
                        value: "single-view",
                      )
                    : const PopupMenuItem(
                        child: Row(children: [Icon(Icons.view_module_outlined), SizedBox(width: marginSize), Text("Multi View")]),
                        value: "multi-view",
                      ),
              hideMenu.value
                  ? const PopupMenuItem(
                      child: Row(children: [Icon(Icons.menu_outlined), SizedBox(width: marginSize), Text("Show Menu")]),
                      value: "show",
                    )
                  : const PopupMenuItem(
                      child: Row(children: [Icon(Icons.menu_outlined), SizedBox(width: marginSize), Text("Hide Menu")]),
                      value: "hide",
                    ),
            ],
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: hideMenu.value ? Colors.transparent : Colors.white),
                borderRadius: BorderRadius.circular(20),
              ),
              padding: const EdgeInsets.all(5),
              child: Icon(
                Icons.menu_outlined,
                color: hideMenu.value ? Colors.transparent : Colors.white,
                size: 30,
              ),
            ),
            onSelected: (value) {
              switch (value) {
                case "play":
                  _simController.play();
                  break;
                case "pause":
                  _simController.pause();
                  break;
                case "restart":
                  _simController.restart();
                  break;
                case "multi-view":
                  _showMultiViewSelector(_simController);
                  break;
                case "single-view":
                  _simController.disableMultiView();
                  break;
                case "show":
                  hideMenu.value = false;
                  break;
                case "hide":
                  hideMenu.value = true;
                  break;
                default:
                  break;
              }
            },
          )),
      right: 20,
      top: 20,
    );
  }
}
