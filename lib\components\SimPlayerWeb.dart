import 'dart:convert';
// import 'dart:io' if (dart.library.html) 'dart:html' as io;
import 'dart:math';
import 'dart:ui' as ui;
import 'dart:async' as asynch;

import 'package:dio/dio.dart';
import 'package:flame/cache.dart';
import 'package:flame/effects.dart';
import 'package:flame/events.dart';
import 'package:flame/text.dart';
import 'package:collection/collection.dart';
// import 'package:flame/cache.dart';
import 'package:flame/components.dart';
import 'package:flame/experimental.dart';
import 'package:flame/image_composition.dart';
import 'package:flame/extensions.dart';
import 'package:flame/flame.dart';
import 'package:flame/game.dart';
import 'package:flame/palette.dart';
import 'package:flame/rendering.dart' as frendering;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart' show Colors, PaintingStyle, Icons, BlendMode;
import 'package:flutter/painting.dart';
import 'package:flutter/widgets.dart' show WidgetsBinding;
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_workers/utils/debouncer.dart';
import 'package:nanoid/nanoid.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:simsushare_player/controllers/ClipboardController.dart';
import 'package:simsushare_player/controllers/SimController.dart';
import 'package:simsushare_player/flame/Labels/builder.dart';
import 'package:simsushare_player/models/Mask.dart';
import 'package:simsushare_player/models/SimObjects.dart';
import 'package:simsushare_player/models/Simulation.dart';
import 'package:simsushare_player/utils/constants.dart';
import 'package:simsushare_player/utils/offset.dart';
import 'package:simsushare_player/utils/timers.dart';
import 'package:string_validator/string_validator.dart';

final highlightPaint = BasicPalette.red.paint()
  ..color = Colors.red
  ..strokeWidth = 2
  ..style = PaintingStyle.stroke;

final shapePaint = BasicPalette.white.paint()
  // ..strokeWidth = 2
  ..style = PaintingStyle.fill;
// ..color = Colors.black

const newMaskColorOpacity = 0.6;
const maskColorOpacity = 0.3;
const shapeOpacityUpdateInterval = 50;

final drawDebouncer = Debouncer(delay: const Duration(milliseconds: 50));

final dioWebAssets = Dio(BaseOptions(baseUrl: "https://sus-assets.s3.amazonaws.com", responseType: ResponseType.bytes));

class SimPlayer extends FlameGame /* with HasDraggableComponents, HasTappableComponents */ {
  bool playMode;
  bool inEditor;
  Scenario? providedSim;
  SimPlayer({this.playMode = true, this.inEditor = false, this.providedSim}); // unlike in the app version, playmode defaults to true
  late SimController _simController;
  late ClipboardController _clipboardController;
  // late SimSpriteComponent background;
  // RectangleComponent? selectedContainer;
  late Image? bgImage;
  List<SimSpriteAnimationComponent> sprites = <SimSpriteAnimationComponent>[];
  Scenario sim = Scenario(name: "", locations: []);
  String id = nanoid(10);
  List<String> hiddenImages = <String>[];
  List<String> hiddenPeople = <String>[];
  final instanceStream = asynch.StreamController<dynamic>.broadcast();
  late double initialSize;
  Vector2 playModeCompensator = Vector2.all(1);
  PositionComponent? selectedComponent;
  final itemsTriggeredOnce = {
    "sprites": {
      "fadeIn": <String>{},
      "fadeOut": <String>{},
    },
    "images": {
      "fadeIn": <String>{},
      "fadeOut": <String>{},
    },
    "people": {
      "fadeIn": <String>{},
      "fadeOut": <String>{},
    },
    "texts": {
      "fadeIn": <String>{},
      "fadeOut": <String>{},
    },
    "shapes": {
      "fadeIn": <String>{},
      "fadeOut": <String>{},
    },
    "jumpers": {
      "fadeIn": <String>{},
      "fadeOut": <String>{},
    },
    "timers": {
      "fadeIn": <String>{},
      "fadeOut": <String>{},
    },
  };

  // @override
  // final images = Images(prefix: "");
  // final assets = AssetsCache(prefix: "");
  // double backgroundVerticalOffset = 0.0;

  double simTime = 0;
  double locationTime = 0;
  double stateTime = 0;
  asynch.Timer _timer = asynch.Timer(Duration.zero, () {});
  // DateTime _lastDraw = DateTime.now();
  // asynch.Timer? _debounceDrawTimer;
  Set<String> playedAudio = <String>{};

  late Image expandIcon;
  late Image rotateIcon;
  late Image volumeIcon;
  late Image playPauseIcon;
  late Image removeIcon;
  // late ui.Canvas mainCanvas;
  Map<String, Image> peopleImages = {};
  final peopleCache = Images(prefix: "assets/people/");
  Map<String, Image> containersImages = {};
  final containersCache = Images(prefix: "assets/containers/");

  @override
  void render(ui.Canvas canvas) {
    camera.viewport.clip(canvas);
    super.render(canvas);
  }

  @override
  void onDetach() {
    // void onRemove() {
    paused = true;
    if (!playMode) {
      print("Before CLEARING ASSETS:");
      Flame.images.clearCache();
    }
    // TODO: stream is not heard by fade in/out functions
    instanceStream.add("detached");
    /* if ((playMode && !inEditor) || !playMode) {
      // Flame.images.clear("background-" + _simController.currentLocation.value.toString());
      Flame.images.clearCache();
    } */
    _timer.cancel();
    // _debounceDrawTimer?.cancel();
    if (!playMode) {
      Future.delayed(const Duration(milliseconds: 50), () {
        _simController.currentSim.value = null;
        _simController.newMask.value = null;
        _simController.currentState.value = -1;
        _simController.currentLocation.value = -1;
      });
    }
    /* try {
      _simController.currentSim.close();
      _simController.newMask.close();
      _simController.currentState.close();
      _simController.currentLocation.close();
    } catch (err) {
      print(err);
    } */
    print("---------------- Detaching Game ---------------- ID: $id");
    super.onDetach();
  }

  @override
  void onGameResize(Vector2 canvasSize) {
    super.onGameResize(canvasSize);
    print("Updating game size Old: ${camera.viewport.size} New: $canvasSize");
    /* 
      NOTE: this is to set the sim width and height when none is set. This is expected to happen only when initializing a new sim
      Point: 1
     */
    if (sim.width == 0 || sim.height == 0 || !playMode) {
      // return;
      sim.width = size.x;
      sim.height = size.y;
    }
    /* if(canvasSize.x / canvasSize.y != 1200/800) {
      print("Updating game size");
      camera.viewport.resize(Vector2(canvasSize.x, canvasSize.x * 800/1200));
    } */
    // final xScale = canvasSize.x / sim.width;
    // final yScale = canvasSize.y / sim.height;
    // final scale = min(xScale, yScale);
    // camera.viewport.resize(Vector2(sim.width * scale, canvasSize.y * scale));
    // sim.width = canvasSize.x;
    // sim.height = canvasSize.y;

    /* 
      NOTE: I don't remember why I had this commented out at some point but removing it causes a difference in the size of the canvas
      which in turn breaks the masking positions
      NOTE: removed again because it broke the size of all locations. Added (Point: 1) as a fix
    */
    // sim.width = size.x;
    // sim.height = size.y;

    // camera.viewport.resize(Vector2(sim.width, canvasSize.y));
    // camera.viewport = FixedResolutionViewport(Vector2(sim.width * scale, sim.heigh * scale));
    // debounceRedraw();
    /* try {
      if (!playMode) {
        // NOTE: must be wrapped in try/catch since it is called on game start and sim is not yet initialized
        sim.width = canvasSize.x;
        sim.height = canvasSize.y;
        camera.viewport = FixedResolutionViewport(Vector2(canvasSize.x, canvasSize.y));
        redraw();
      }
    } catch (err) {
      print(err);
    } */
  }

  // TODO: maybe override this to handle the update of the game size but without affecting the masks for now
  /* @override
  void onGameResize(Vector2 canvasSize) {
    print("Game resized to: $canvasSize for ID: $id in playmode: $playMode");
    // final _simController = Get.find<SimController>();
    // final sim = _simController.currentSim.value!;
    if (sim == null) {
      return;
    }
    super.onGameResize(canvasSize);
    // for (final mask in simMasks) {
    for (final mask in sim.masks) {
      mask.coordinates = mask.coordinates.map((e) {
        e.x = e.x * canvasSize.x / sim.width;
        e.y = e.y * canvasSize.y / sim.height;
        return e;
      }).toList();
    }
    // if (!playMode) {
    sim.width = canvasSize.x;
    sim.height = canvasSize.y;
    // }
    // redraw();
    debounceRedraw();
  } */

  @override
  Future<void> onLoad() async {
    // debugMode = true;
    // editor [983,727]
    // preview [1243, 800]
    print("Game ID: $id and playMode: $playMode");
    print("Game size: $size");
    // initialSize = size.x / 8;

    // double getXWidth = Get.width;
    // double getXHeight = Get.height;
    // double getXWidth =  givenSize?.width ?? size.x;
    // double getXHeight = givenSize?.height ?? size.y;
    double getXWidth = WidgetsBinding.instance.platformDispatcher.views.first.physicalSize.width;
    double getXHeight = WidgetsBinding.instance.platformDispatcher.views.first.physicalSize.height;

    /* if (Get.width == null) {
      getXWidth = size.x;
      getXHeight = size.y;
    } */

    // initialSize = getInitialSize();
    initialSize = size.x / 8;

    await super.onLoad();

    // camera.viewport = FixedAspectRatioViewport(aspectRatio: 12 / 8);
    // camera.viewport = FixedSizeViewport(1200, 800);
    // camera.viewport = FixedResolutionViewport(Vector2(1200, 800));
    print("Visible game size: ${camera.viewfinder.visibleGameSize}");
    // camera.viewfinder.visibleGameSize = Vector2(size.x, size.y);
    // camera.viewfinder.visibleGameSize = Vector2(size.x, size.x * 12 / 8);
    print("======= Original size: ${size.x} x ${size.y}");
    if (playMode) {
      /* double intendedWidth = getXWidth;
      double intendedHeight = getXWidth * 8 / 12;
      if (intendedHeight > getXHeight) {
        intendedHeight = getXHeight;
        intendedWidth = getXHeight * 12 / 8;
      }
      print("======= Intended width: $intendedWidth and height: $intendedHeight (Play Mode)");
      print("======= Get Size: ${getXWidth} x ${getXHeight}");
      // camera = CameraComponent.withFixedResolution(width: intendedWidth, height: intendedHeight); */
      camera = CameraComponent.withFixedResolution(width: sim.width, height: sim.height);
      // playModeCompensator = Vector2(1200 / 930, 800 / 620);
      // playModeCompensator = Vector2(1200 / sim.width, 800 / sim.height);
    } else {
      double intendedWidth = canvasSize.x;
      double intendedHeight = canvasSize.x * 8 / 12;
      if (intendedHeight > canvasSize.y) {
        intendedHeight = canvasSize.y;
        intendedWidth = canvasSize.y * 12 / 8;
      }
      print("======= Intended width: $intendedWidth and height: $intendedHeight (Editor Mode)");
      print("======= Canvas Size: ${canvasSize.x} x ${canvasSize.y}");
      camera = CameraComponent.withFixedResolution(width: intendedWidth, height: intendedHeight);
    }
    // print("Visible game size after: ${camera.viewfinder.visibleGameSize}");
    // camera = CameraComponent.withFixedResolution(width: 1200, height: 800);
    _simController = Get.find();
    _clipboardController = Get.find();
    sim = playMode ? (providedSim?.copy() ?? _simController.currentSim.value!.copy()) : _simController.currentSim.value!;

    // print("Provided Sim: $providedSim");
    print("Player sim size: ${sim.width} x ${sim.height} and play size: ${size.x} x ${size.y}");

    // if (sim.width.toInt() != size.x.toInt() || sim.height.toInt() != size.y.toInt()) {
    //   final widthScale = size.x / sim.width;
    //   final heightScale = size.y / sim.height;
    //   print("Updating sim size with scale: $widthScale x $heightScale");
    //   sim.masks = sim.masks.map((mask) => mask.scale(widthScale, heightScale)).toList();
    //   // sim.width = size.x;
    //   // sim.height = size.y;
    // }

    /* if (playMode && inEditor) {
      // convert all mask points to percentages
      sim.masks = sim.masks.map((mask) {
        final m = mask.copy(clone: true);
        m.coordinates = m.coordinates.map((e) {
          e.x = e.x / sim.width;
          e.y = e.y / sim.height;
          return e;
        }).toList();
        return m;
      }).toList();
      // camera.viewport.canvasSize = Vector2(sim.width, sim.height);
      // camera.viewport = FixedResolutionViewport(Vector2(sim.width, sim.height));
      // camera.viewport = DynamicResolutionViewport(Vector2(sim.width, sim.height));
      // camera.viewport.resize(canvasSize);
    } */

    if (playMode) {
      // get all images in every sim location and add to hiddenImages if showOnStart is false

      for (final loc in sim.locations) {
        for (final img in loc.images) {
          if (img.hideOnStart) {
            hiddenImages.add(img.id);
          }
        }
        for (final person in loc.people) {
          if (person.hideOnStart) {
            hiddenPeople.add(person.id);
          }
        }
      }

      _timer = asynch.Timer.periodic(const Duration(seconds: 1), (t) {
        if (!playMode) {
          _timer.cancel();
        }
        const secs = 1;
        // update sim time
        simTime += secs;

        // update location time
        locationTime += secs;
        // print("Location time updated to: $locationTime");
        if (_simController.currentLocation.value == -1) {
          if (_timer.isActive) {
            _timer.cancel();
          }
          return;
        }
        final jumperTimed =
            sim.locations[_simController.currentLocation.value].jumpers.firstWhereOrNull((jmp) => jmp.delay > 0 && jmp.delay <= locationTime);
        if (jumperTimed != null && sim.inPlayMode) {
          // print(
          //     "BEFORE JUMP, TIMER IS ACTIVE: ${_timer.isActive}, AND PLAY MODE: $playMode, AND SIM IN PLAY MODE VALUE: ${_simController.currentSim.value!.inPlayMode}");
          // print("Before Jumping to location with ID: $id and playMode: $playMode and sim.inPlayMode: ${sim.inPlayMode}");
          _simController.jumpToLocation(jumperTimed.to);
          _simController.currentSim.refresh();
        }
        // update state time
        stateTime += secs;

        // update timer components
        _simController.currentSim.value!.locations[_simController.currentLocation.value].timers.forEachIndexed((index, timer) {
          // final comp = findByKeyName(timer.id);
          final comp = findByKeyName("timer-$index");
          if (comp == null) return;
          String text;
          switch (timer.type) {
            case SimTimerType.timeOfDay:
              final time = DateTime.now();
              text = getTimerTextFromDate(time, timer.format);
              break;
            case SimTimerType.countdown:
              final difference = timer.startingSecond - simTime.toInt();
              text = secondsToTimerText(difference, timer.format);
              break;
            case SimTimerType.exercise:
              final sum = timer.startingSecond + simTime.toInt();
              text = secondsToTimerText(sum, timer.format);
              break;
            case SimTimerType.location:
              final sum = timer.startingSecond + locationTime.toInt();
              text = secondsToTimerText(sum, timer.format);
              break;
            case SimTimerType.state:
              final sum = timer.startingSecond + stateTime.toInt();
              text = secondsToTimerText(sum, timer.format);
              break;
            default:
              text = "Invalid";
              print("Unhandled timer type: ${timer.type}");
          }
          (comp as SimTextComponent).text = text;
        });
      });
    }

    // Only listen to sim changes if no providedSim was given
    if (providedSim == null) {
      _simController.currentSim.listen((sim) {
        print("TAG: Updated sim: ${sim?.id} and inPlayMode: ${sim?.inPlayMode}");
        EasyDebounce.debounce('simplayer _simController.currentSim.listen', const Duration(milliseconds: 50), () async {
          /* if (sim != null) {
            print("TAG: SIM NOT NULL SETTING PLAYMODE: ${StackTrace.current}");
            playMode = sim.inPlayMode;
          } */
          if (sim != null && !sim.inPlayMode) {
            print("Adding sim to undo stack");
            _simController.addToUndoStack(sim.copy(clone: true));
            print("undo stack: ${_simController.undoStack.length}");
          }
          /* final now = DateTime.now();
          if (now.difference(_lastDraw).inMilliseconds < 20) {
            print("Won't draw");
            return;
          }
          _lastDraw = now; */
          // redraw();
          debounceRedraw();
          // debounce redraw causes delayed movement of sprites and images causing duplicates
          // debounceRedraw();
        });
      });
    }
    _simController.newMask.listen((mask) {
      if (mask != null) {
        // redraw();
        debounceRedraw();
      }
    });
    _simController.currentState.listen((stateIndex) {
      EasyDebounce.debounce('simplayer _simController.currentState.listen', const Duration(milliseconds: 50), () async {
        stateTime = 0;
      });
    });
    // Only listen to controller changes if no providedSim was given
    if (providedSim == null) {
      _simController.currentLocation.listen((locationIndex) async {
        EasyDebounce.debounce('simplayer _simController.currentLocation.listen', const Duration(milliseconds: 50), () async {
          // try {
          // remove(background);
          // } catch (err) {
          //   print(err);
          // }
          locationTime = 0;
          print("Location index changed");
          // background = SpriteComponent();
          // setBackground();
          // Image bg;
          // if (kIsWeb) {
          //   bg = await Flame.images.fromBase64("background", sim.locations[_simController.currentLocation.value].image);
          // } else {
          //   final img = base64Encode(await File(sim.locations[_simController.currentLocation.value].image).readAsBytes());
          //   bg = await Flame.images.fromBase64("background", img);
          // }
          // background
          //   ..sprite = Sprite(bg)
          //   ..size = size;
          // await add(background);
          return redraw();
        });
      });
    }
    _simController.backgroundNotifier.addListener(() {
      print("Background changed");
      Flame.images.clear("background-$id-${_simController.currentLocation.value.toString()}");
      removeAll(children);
      redraw();
    });
    _simController.signalStream.stream.listen((event) {
      EasyDebounce.debounce("sim-player-signal-stream", const Duration(milliseconds: 500), () {
        switch (event) {
          case "play":
            resumeEngine();
            break;
          case "pause":
            pauseEngine();
            break;
          case "restart":
            _simController.selectedSimObjectIndex.value = -1;
            _simController.selectedType.value = null;
            simTime = 0;
            locationTime = 0;
            stateTime = 0;
            redraw();
            // _simController.currentLocation.value = 0;
            // _simController.currentState.value = 0;
            // _simController.currentSim.refresh();
            break;
          case "new-state":
            buildBgCache();
            break;
          case "reorder":
            buildBgCache(clearCache: true);
            break;
          default:
            print("Unhandled signal in SimPlayerWeb: $event");
        }
      });
    });
    expandIcon = await Flame.images.load("expand.png");
    rotateIcon = await Flame.images.load("rotate-arrow.png");
    volumeIcon = await Flame.images.load("volume.png");
    removeIcon = await Flame.images.load("remove.png");
    playPauseIcon = await Flame.images.load("pause_filled.png");
    print("Finish Main Tools Images");
    final peopleFutures = <Future>[];
    for (var curr in peoplePostureCount.keys) {
      final count = peoplePostureCount[curr]!;
      for (var i = 0; i < count; i++) {
        // peopleImagesFutures["$curr-$i"] = peopleCache.load("$curr-$i", "assets/people/$curr/$i.png");
        // peopleImages["$curr-$i"] = await peopleCache.load("$curr/$i.png", key: "$curr-$i");
        peopleFutures.add(() async {
          final response = await dioWebAssets.get<Uint8List>("/people/$curr/$i.png");
          return peopleImages["$curr-$i"] = await decodeImageFromList(response.data!);
          /* ui.decodeImageFromList(response.data!, (result) {
            peopleImages["$curr-$i"] = result;
          }); */
        }());
      }
    }
    final containersFutures = <Future>[];
    for (var curr in containerViewsMapping.keys) {
      final count = containerViewsMapping[curr]!.length;
      for (var i = 0; i < count; i++) {
        // peopleImagesFutures["$curr-$i"] = peopleCache.load("$curr-$i", "assets/people/$curr/$i.png");
        // containersImages["$curr-$i"] = await containersCache.load("${containerAssetsMapping[curr]}/$i.png", key: "$curr-$i");
        containersFutures.add(() async {
          final response = await dioWebAssets.get<Uint8List>("/containers/${containerAssetsMapping[curr]}/$i.png");
          containersImages["$curr-$i"] = await decodeImageFromList(response.data!);
          /* ui.decodeImageFromList(response.data!, (result) {
            containersImages["$curr-$i"] = result;
          ); */
        }());
      }
    }
    await Future.wait(peopleFutures);
    await Future.wait(containersFutures);
    print("Finish onLoad");
    await buildBgCache();
    if (providedSim != null) {
      // When using providedSim, we need to manually trigger the initial redraw
      // since we don't listen to controller changes
      await redraw();
    } else {
      _simController.currentSim.refresh();
    }
    // await redraw();
  }

  Future buildBgCache({clearCache = false}) async {
    // NOTE: need to pause to avoid old backgrounds displosal from throwing errors
    paused = true;
    if (clearCache) {
      for (int i = 0; i < sim.locations.length; i++) {
        Flame.images.clear("background-$id-$i");
      }
    }
    await Future.wait(sim.locations.mapIndexed((index, e) async {
      /* 
      // NOTE: this is to clear the cache when the background is changed. 
      // this was removed since it breaks the background when a new state is added

      if (Flame.images.containsKey("background-$id-${index.toString()}")) {
        Flame.images.clear("background-$id-${index.toString()}");
      } 
      */
      /* if (kIsWeb) {
        return Flame.images.fromBase64(
            "background-$id-${_simController.currentLocation.value.toString()}", sim.locations[_simController.currentLocation.value].image);
      } else {
        final img = base64Encode(await File(sim.locations[_simController.currentLocation.value].image).readAsBytes());
        return Flame.images.fromBase64("background-$id-${_simController.currentLocation.value.toString()}", img);
      } */
      final cacheName = "background-$id-${index.toString()}";
      return Flame.images.add(
          cacheName,
          await decodeImageFromList(isBase64(sim.locations[index].image)
              ? base64Decode(sim.locations[index].image)
              : Uint8List.fromList(sim.locations[index].image.codeUnits)));
    }));
    paused = false;
    if (clearCache) {
      redraw();
    }
  }

  void debounceRedraw() {
    drawDebouncer.call(() {
      redraw();
    });
    /* // if (_debounceDrawTimer?.isActive ?? false) _debounceDrawTimer?.cancel();
    if (_debounceDrawTimer?.isActive ?? false) return;
    // NOTE: debounce redraw is smooth at 16 but is too fast when redrawing updates for a label and causing the player to crash
    _debounceDrawTimer = asynch.Timer(const Duration(milliseconds: 50 /* 16 */), () {
      if (_simController.currentSim.value == null || _simController.currentLocation.value == -1) return;
      redraw();
    }); */
  }

  void setShapeFromType(SimShape shape, SimShapeComponent sc, ui.Paint simShapePaint) {
    switch (shape.shape) {
      case "black-windows":
        sc.shape = RectangleComponent(
          size: sc.size,
          paint: simShapePaint,
        );
        break;
      case "rectangle":
      case "4":
        print("Putting rectangle");
        sc.shape = RectangleComponent(
          size: sc.size,
          paint: simShapePaint,
          scale: Vector2(1.5, 1),
        );
        break;
      case "rounded-rectangle":
      case "8":
        print("Putting rounded rectangle");
        sc.shape = PolygonComponent.relative(
          [
            // Top left
            Vector2(-1, 0.95),
            Vector2(-0.985, 0.985),
            Vector2(-0.95, 1),
            // Top right
            Vector2(0.95, 1),
            Vector2(0.985, 0.985),
            Vector2(1, 0.95),
            // Bottom right
            Vector2(1, -0.95),
            Vector2(0.985, -0.985),
            Vector2(0.95, -1),
            // Bottom left
            Vector2(-0.95, -1),
            Vector2(-0.985, -0.985),
            Vector2(-1, -0.95),
          ],
          parentSize: sc.size,
          paint: simShapePaint,
        );
        break;
      case "square":
      case "5":
        print("Putting square");
        sc.shape = RectangleComponent(
          size: sc.size,
          paint: simShapePaint,
        );
        break;
      case "circle":
      case "6":
        sc.shape = CircleComponent(
          anchor: Anchor.center,
          radius: sc.size.x / 2,
          paint: simShapePaint,
        );
        break;
      case "triangle":
      case "7":
        sc.shape = PolygonComponent.relative(
          [
            Vector2(0, -1),
            Vector2(-1, 1),
            Vector2(1, 1),
          ],
          parentSize: sc.size,
          paint: simShapePaint,
        );
        break;
      case "arrow":
      case "arrow-1":
      case "arrow-2":
      case "arrow-3":
      case "arrow-4":
      case "0":
      case "1":
      case "2":
      case "3":
        sc.shape = PolygonComponent.relative(
          [
            Vector2(-1, -0.22),
            Vector2(0.5, -0.22),
            Vector2(-0.1, -0.85),
            Vector2(0.3, -0.85),
            Vector2(1, 0),
            Vector2(0.3, 0.85),
            Vector2(-0.1, 0.85),
            Vector2(0.5, 0.22),
            Vector2(-1, 0.22),
          ],
          parentSize: sc.size,
          paint: simShapePaint,
        );
        break;
      default:
        return print("Invalid shape type: ${shape.shape} and runtime type: ${shape.runtimeType}");
    }
    sc.shape!.isSolid = false;
  }

  PositionComponent buildWidenComponent(PositionComponent pc, SimObject so, {bool evenScale = false, Vector2? size}) {
    if (playMode) return SimSpriteComponent(onDragged: (_, __) {}, onTapped: (_, __) {});
    SimSpriteComponent? widenComponent;
    widenComponent = SimSpriteComponent(
        onDragged: (_, dragEvent) {
          final initialSize = pc.size.clone();
          initialSize.x = initialSize.x / so.widthScale;
          initialSize.y = initialSize.y / so.heightScale;
          Vector2 updatedScale;
          if (evenScale) {
            final sizeFactor = min(dragEvent.delta.x, dragEvent.delta.y);
            // final scaleFactor = sizeFactor / canvasSize.x * 10;
            // final scaleFactor = sizeFactor / canvasSize.x * 1;
            final scaleFactor = sizeFactor;
            if (pc.size.x + scaleFactor < 10 || pc.size.y + scaleFactor < 10) return; // NOTE: Size is calculated in pixels not scale
            pc.size += Vector2.all(scaleFactor);
            updatedScale = pc.size.clone();
            updatedScale.x = updatedScale.x / initialSize.x;
            updatedScale.y = updatedScale.y / initialSize.y;
            // so
            //   ..widthScale += scaleFactor / canvasSize.x * 4
            //   ..heightScale += scaleFactor / canvasSize.x * 4;
          } else {
            if (pc.size.x + dragEvent.delta.x < 10 || pc.size.y + dragEvent.delta.y < 10) return; // NOTE: Size is calculated in pixels not scale
            pc.size += dragEvent.delta;
            updatedScale = pc.size.clone();
            updatedScale.x = updatedScale.x / initialSize.x;
            updatedScale.y = updatedScale.y / initialSize.y;
            // sim.locations[_simController.currentLocation.value].shapes.firstWhere((s) => s.id == shape.id)
            /* so
              ..widthScale += ((dragEvent.delta.x / canvasSize.x) * 2)
              ..heightScale += ((dragEvent.delta.y / canvasSize.y) * 2); */
          }
          so.widthScale = updatedScale.x;
          so.heightScale = updatedScale.y;
          /* final controls = findByKeyName("controls");
          print("On widen --> controls: ${controls.runtimeType}");
          if (controls != null) {
            (controls as PositionComponent).size = pc.size;
            controls.size = pc.size;
            controls.position = Vector2(pc.x - (pc.size.x * pc.scale.x / 2), pc.y - (pc.size.y * pc.scale.y / 2));
          } */

          // this is a fix for the children not updating their size when the parent is updated. it isn't perfect though
          /* for (var child in pc.children) {
            (child as PositionComponent).size = pc.size;
          } */
          // redraw();
          // debounceRedraw();
        },
        onTapped: (comp, tapEvent) {},
        onDragComplete: (comp) {
          _simController.currentSim.refresh();
          // redraw();
        })
      ..sprite = Sprite(expandIcon)
      ..size = Vector2(15, 15)
      ..position = (size ?? pc.size) - Vector2(16, 16)
      // TODO: parent filter color still overrides this
      ..paint = (ui.Paint()..color = Colors.white)
      ..decorator.addLast(frendering.PaintDecorator.blur(0))
      // ..decorator.addLast(frendering.PaintDecorator.tint(const ui.Color.fromARGB(214, 255, 255, 255)))
      ..decorator.addLast(frendering.PaintDecorator.tint(const ui.Color.fromARGB(255, 255, 255, 255)))
      ..priority = pc.priority
      ..flipVerticallyAroundCenter();

    return PositionComponent(
      size: Vector2(12, 12),
      children: [
        RectangleComponent(
          size: Vector2(18, 18),
          paint: ui.Paint()..color = Colors.red,
          position: (size ?? pc.size) - Vector2(18, 18),
        ),
        widenComponent,
      ],
    );
  }

  SimSpriteComponent buildRotationComponent(PositionComponent pc, SimObject so, {Vector2? multiper, Vector2? size}) {
    if (playMode) return SimSpriteComponent(onDragged: (_, __) {}, onTapped: (_, __) {});
    final imgSize = Vector2.all(min(pc.width, pc.height));
    // const scaleMultiplier = 0.6;
    final maxScale = max(max(so.widthScale, so.heightScale), so.scale);
    final scaleMultiplier = (so.scale > 1 || so.widthScale > 1 || so.heightScale > 1) ? 0.6 / maxScale : 0.70 * maxScale;
    final rotationComponent = SimSpriteComponent(
      onDragStarted: () {
        // pc.anchor = Anchor.center;
        // pc.position += (pc.size / 2);
      },
      onDragged: (_, dragEvent) {
        so.rotation += dragEvent.delta.y * 180 / pi / 12;
        pc.angle += dragEvent.delta.y / 12;
      },
      onDragComplete: (_) {
        // so.rotation += pc.angle;
        // so.position -= (so.size / 2);
        // so.anchor = Anchor.topLeft;
        // _simController.currentSim.refresh();
      },
      onTapped: (comp, tapEvent) {},
    )
      ..sprite = Sprite(rotateIcon)
      // ..sprite = Sprite(rotateIcon..size.setFrom(imgSize))
      ..size = size ?? imgSize
      // ..size = Vector2(pc.width, pc.height)
      ..position = Vector2(
        (size?.x ?? pc.width) * (multiper?.x ?? 1) * ((1 - scaleMultiplier) / 2),
        (size?.y ?? pc.height) * (multiper?.y ?? 1) * ((1 - scaleMultiplier) / 2),
      )
      // ..position = pc.center
      // TODO: parent filter color still overrides this
      ..paint = (ui.Paint()..color = Colors.black)
      ..decorator.addLast(frendering.PaintDecorator.blur(0))
      ..decorator.addLast(frendering.PaintDecorator.tint(Colors.black))
      ..scale = Vector2(scaleMultiplier, scaleMultiplier)
      ..priority = pc.priority;
    // ..scale = Vector2(0.7, 0.7);
    return rotationComponent;
  }

  PositionComponent buildRemoveComponent(PositionComponent pc, SimObject so, {Vector2? size}) {
    if (playMode) return SimSpriteComponent(onDragged: (_, __) {}, onTapped: (_, __) {});
    final removeComponent = SimSpriteComponent(
      onDragged: (_, dragEvent) {},
      onTapped: (comp, tapEvent) {
        switch (_simController.selectedType.value) {
          case SimObjectType.sprite:
            _simController.currentSim.value!.locations[_simController.currentLocation.value].sprites
                .removeAt(_simController.selectedSimObjectIndex.value);
            break;
          case SimObjectType.text:
            _simController.currentSim.value!.locations[_simController.currentLocation.value].texts
                .removeAt(_simController.selectedSimObjectIndex.value);
            break;
          case SimObjectType.image:
            _simController.currentSim.value!.locations[_simController.currentLocation.value].images
                .removeAt(_simController.selectedSimObjectIndex.value);
            break;
          case SimObjectType.audio:
            _simController.currentSim.value!.locations[_simController.currentLocation.value].sounds
                .removeAt(_simController.selectedSimObjectIndex.value);
            break;
          case SimObjectType.shape:
            _simController.currentSim.value!.locations[_simController.currentLocation.value].shapes
                .removeAt(_simController.selectedSimObjectIndex.value);
            break;
          case SimObjectType.locationJumper:
            _simController.currentSim.value!.locations[_simController.currentLocation.value].jumpers
                .removeAt(_simController.selectedSimObjectIndex.value);
            break;
          case SimObjectType.label:
            _simController.currentSim.value!.locations[_simController.currentLocation.value].labels
                .removeAt(_simController.selectedSimObjectIndex.value);
            break;
          case SimObjectType.container:
            _simController.currentSim.value!.locations[_simController.currentLocation.value].containers
                .removeAt(_simController.selectedSimObjectIndex.value);
            break;
          case SimObjectType.person:
            _simController.currentSim.value!.locations[_simController.currentLocation.value].people
                .removeAt(_simController.selectedSimObjectIndex.value);
            break;
          case SimObjectType.timer:
            _simController.currentSim.value!.locations[_simController.currentLocation.value].timers
                .removeAt(_simController.selectedSimObjectIndex.value);
            break;
          case SimObjectType.mask:
            _simController.currentSim.value!.masks.removeAt(_simController.selectedSimObjectIndex.value);
            break;
          default:
            print("Invalid Sim Selected Type: ${_simController.selectedType.value}");
        }
        _simController.selectedSimObjectIndex.value = -1;
        _simController.selectedType.value = null;
        _simController.currentSim.refresh();
        _clipboardController.clearSelection();
      },
      onDragComplete: (comp) {},
    )
      ..sprite = Sprite(removeIcon)
      ..size = Vector2(12, 12)
      ..position = Vector2((size?.x ?? pc.width) - 13, 2)
      ..decorator.addLast(frendering.PaintDecorator.tint(Colors.white))
      ..priority = pc.priority;

    return PositionComponent(
      size: Vector2(12, 12),
      children: [
        RectangleComponent(
          size: Vector2(15, 15),
          paint: ui.Paint()..color = Colors.red,
          position: Vector2((size?.x ?? pc.width) - 14, 0),
        ),
        removeComponent,
      ],
    );
  }

  void drawControls() {
    final obj = _simController.getCurrentSelectedObject()!;
    final largestSide = selectedComponent!.size.x >= selectedComponent!.size.y ? selectedComponent!.size.x : selectedComponent!.size.y;
    final selectedContainer = RectangleComponent(
        position: Vector2(selectedComponent!.x /* - (selectedComponent!.size.x * selectedComponent!.scale.x / 2) */,
            selectedComponent!.y /* - (selectedComponent!.size.y * selectedComponent!.scale.y / 2) */),
        // size: selectedComponent!.size,
        size: Vector2.all(largestSide),
        anchor: Anchor.center,
        paint: highlightPaint,
        // angle: obj.rotation * pi / 180,
        priority: 1000000,
        scale: selectedComponent!.scale,
        key: ComponentKey.named("controls"));
    bool evenScale = false;
    if (obj is SimShape || obj is SimLabel) {
      evenScale = true;
    }
    final widenComponent = buildWidenComponent(selectedComponent!, obj, evenScale: evenScale, size: Vector2.all(largestSide));
    final rotationComponent = buildRotationComponent(selectedComponent!, obj, size: Vector2.all(largestSide));
    final removeComponent = buildRemoveComponent(selectedComponent!, obj, size: Vector2.all(largestSide));
    selectedContainer.addAll([/* if (!evenScale)  */ widenComponent, rotationComponent, removeComponent]);
    add(selectedContainer);
  }

  List<SimShapeComponent> generateMaskEditorVertices(Mask mask, {double radius = 4}) {
    List<SimShapeComponent> vertices = [];
    for (var index = 0; index < mask.coordinates.length; index++) {
      final coor = mask.coordinates[index];
      final vertix = SimShapeComponent(
          onDragged: (comp, _) {
            print("Dragging vertex");
            mask.coordinates[index] = Coordinate(comp.x, comp.y);
            if (_simController.newMask.value != null) {
              _simController.newMask.refresh();
            } else {
              _simController.currentSim.refresh();
            }
          },
          onTapped: (comp, _) {
            print("Tapped vertex $index");
            mask.coordinates.removeAt(index);
            if (_simController.newMask.value != null) {
              _simController.newMask.refresh();
            } else {
              _simController.currentSim.refresh();
            }
          },
          shape: CircleComponent(
            // anchor: Anchor.center,
            // position: Vector2.all(-radius),
            radius: radius,
          )..setColor(Colors.black))
        ..anchor = Anchor.center
        ..position = Vector2(coor.x, coor.y)
        ..priority = 90000
        ..size = Vector2.all(radius * 2);
      final upcoming = mask.coordinates[index != mask.coordinates.length - 1 ? index + 1 : 0];
      final midPoint = SimShapeComponent(
          onDragged: (comp, _) {},
          onTapped: (comp, _) {
            print("Tapped vertex $index");
            mask.coordinates.insert(index + 1, Coordinate((coor.x + upcoming.x) / 2, (coor.y + upcoming.y) / 2));
            if (_simController.newMask.value != null) {
              _simController.newMask.refresh();
            } else {
              _simController.currentSim.refresh();
            }
          },
          shape: CircleComponent(
            // anchor: Anchor.center,
            // position: Vector2.all(-radius),
            radius: radius,
          )..setColor(Colors.white))
        ..anchor = Anchor.center
        ..position = Vector2((coor.x + upcoming.x) / 2, (coor.y + upcoming.y) / 2)
        ..priority = 90000
        ..size = Vector2.all(radius * 2);
      vertices.add(midPoint);
      vertices.add(vertix);
    }
    /* return mask.coordinates
        .mapIndexed(
          (index, coor) => SimShapeComponent(
              onDragged: (comp) {
                print("Dragging vertex");
                mask.coordinates[index] = Coordinate(comp.x, comp.y);
                if (_simController.newMask.value != null) {
                  _simController.newMask.refresh();
                } else {
                  _simController.currentSim.refresh();
                }
              },
              onTapped: (_) {
                print("Tapped vertex $index");
                mask.coordinates.removeAt(index);
                if (_simController.newMask.value != null) {
                  _simController.newMask.refresh();
                } else {
                  _simController.currentSim.refresh();
                }
              },
              shape: CircleComponent(
                // anchor: Anchor.center,
                // position: Vector2.all(-radius),
                radius: radius,
              )..setColor(Colors.black))
            ..anchor = Anchor.center
            ..position = Vector2(coor.x, coor.y)
            ..priority = 90000
            ..size = Vector2.all(radius * 2),
        )
        .toList(); */
    return vertices;
  }

  // NOTE: the fade in fade out effect time might not sync exactly to the time if the opacity of the object is not 1 or 0 respectively
  Future<void> createShapeFadeIn(SimShape so, SimShapeComponent comp) async {
    print("Reached create shape fade in");
    if (so.fadeInWhen > 0) {
      comp.shape!.paint.color = comp.shape!.paint.color.withOpacity(0);
    }
    bool active = true;
    final sub = instanceStream.stream.listen((event) {
      if (event == "detached") {
        active = false;
      }
    });
    // will fade in to the set opacity value not 1
    await Future.delayed(Duration(milliseconds: ((so.fadeInWhen + 0.1) * 1000).toInt()), () async {
      comp.shape!.paint.color = comp.shape!.paint.color.withOpacity(0);
      print("Reached fade in delayed effect");
      await Future.doWhile(
        () => Future.delayed(
          const Duration(milliseconds: shapeOpacityUpdateInterval),
          () async {
            if (!active) return false;
            if (comp.shape!.paint.color.opacity == so.opacity) {
              return false;
            }
            final postOpacity = comp.shape!.paint.color.opacity + (shapeOpacityUpdateInterval / (1000 * so.fadeInDuration));
            if (postOpacity >= so.opacity) {
              comp.shape!.paint.color = comp.shape!.paint.color.withOpacity(1);
              return false;
            }
            comp.shape!.paint.color = comp.shape!.paint.color.withOpacity(postOpacity);
            return true;
          },
        ),
      );
      print("Finished delayed effect loop fadein");
    });
    sub.cancel();
  }

  Future<void> createShapeFadeOut(SimShape so, SimShapeComponent comp) async {
    bool active = true;
    final sub = instanceStream.stream.listen((event) {
      print("Received event $event in fadeout");
      if (event == "detached") {
        active = false;
      }
    });
    await Future.delayed(Duration(milliseconds: ((so.fadeInWhen + so.fadeInDuration + so.fadeOutWhen + 0.1) * 1000).toInt()), () async {
      print("Reached fade out delayed effect");
      await Future.doWhile(
        () => Future.delayed(
          const Duration(milliseconds: shapeOpacityUpdateInterval),
          () async {
            if (!active) return false;
            print("Reached delayed effect loop");
            if (comp.shape!.paint.color.opacity == 0) {
              return false;
            }
            final postOpacity = comp.shape!.paint.color.opacity - (shapeOpacityUpdateInterval / (1000 * so.fadeOutDuration));
            if (postOpacity <= 0) {
              comp.shape!.paint.color = comp.shape!.paint.color.withOpacity(0);
              return false;
            }
            comp.shape!.paint.color = comp.shape!.paint.color.withOpacity(postOpacity);
            return true;
          },
        ),
      );
      print("Finished delayed effect loop fadeout");
    });
    sub.cancel();
  }

  Future<void> createTextFadeIn(SimText st, SimTextComponent comp) async {
    print("Reached create text fade in");
    final targetFilterColor = st.filterColor;
    final targetBackgroundColor = st.backgroundColor;
    if (st.fadeInWhen > 0) {
      comp.textRenderer = TextPaint(
        style: TextStyle(
          color: targetFilterColor.withOpacity(0),
          fontSize: 24,
          backgroundColor: targetBackgroundColor.withOpacity(0),
        ),
      );
    }
    bool active = true;
    final sub = instanceStream.stream.listen((event) {
      if (event == "detached") {
        active = false;
      }
    });
    // will fade in to the set opacity value not 1
    await Future.delayed(Duration(milliseconds: ((st.fadeInWhen + 0.1) * 1000).toInt()), () async {
      print("Reached fade in delayed effect");
      comp.textRenderer = TextPaint(
        style: TextStyle(
          color: targetFilterColor.withOpacity(0),
          fontSize: 24,
          backgroundColor: targetBackgroundColor.withOpacity(0),
        ),
      );
      await Future.doWhile(
        () => Future.delayed(
          const Duration(milliseconds: shapeOpacityUpdateInterval),
          () async {
            if (!active) return false;
            final tStyle = (comp.textRenderer as TextPaint).style;
            if (tStyle.color!.opacity == st.opacity) {
              return false;
            }
            final postOpacity = tStyle.color!.opacity + (shapeOpacityUpdateInterval / (1000 * st.fadeInDuration));
            if (postOpacity >= st.opacity) {
              comp.textRenderer = TextPaint(
                style: TextStyle(
                  color: targetFilterColor.withOpacity(1),
                  fontSize: 24,
                  backgroundColor: targetBackgroundColor.opacity == 0 ? Colors.transparent : targetBackgroundColor.withOpacity(1),
                ),
              );
              return false;
            }
            comp.textRenderer = TextPaint(
              style: TextStyle(
                color: targetFilterColor.withOpacity(postOpacity),
                fontSize: 24,
                backgroundColor: targetBackgroundColor.opacity == 0 ? Colors.transparent : targetBackgroundColor.withOpacity(postOpacity),
              ),
            );
            return true;
          },
        ),
      );
      print("Finished delayed effect loop fadein");
    });
    comp.textRenderer = TextPaint(
      style: TextStyle(
        color: targetFilterColor,
        fontSize: 24,
        backgroundColor: targetBackgroundColor,
      ),
    );
    sub.cancel();
  }

  Future<void> createTimerFadeIn(SimTimer st, SimTextComponent comp) async {
    print("Reached create text fade in");
    final targetFilterColor = st.filterColor;
    if (st.fadeInWhen > 0) {
      comp.textRenderer = TextPaint(
        style: TextStyle(
          color: targetFilterColor.withOpacity(0),
          fontSize: 24,
        ),
      );
    }
    bool active = true;
    final sub = instanceStream.stream.listen((event) {
      if (event == "detached") {
        active = false;
      }
    });
    // will fade in to the set opacity value not 1
    await Future.delayed(Duration(milliseconds: ((st.fadeInWhen + 0.1) * 1000).toInt()), () async {
      print("Reached fade in delayed effect");
      comp.textRenderer = TextPaint(
        style: TextStyle(
          color: targetFilterColor.withOpacity(0),
          fontSize: 24,
        ),
      );
      await Future.doWhile(
        () => Future.delayed(
          const Duration(milliseconds: shapeOpacityUpdateInterval),
          () async {
            if (!active) return false;
            final tStyle = (comp.textRenderer as TextPaint).style;
            if (tStyle.color!.opacity == st.opacity) {
              return false;
            }
            final postOpacity = tStyle.color!.opacity + (shapeOpacityUpdateInterval / (1000 * st.fadeInDuration));
            if (postOpacity >= st.opacity) {
              comp.textRenderer = TextPaint(
                style: TextStyle(
                  color: targetFilterColor.withOpacity(1),
                  fontSize: 24,
                ),
              );
              return false;
            }
            comp.textRenderer = TextPaint(
              style: TextStyle(
                color: targetFilterColor.withOpacity(postOpacity),
                fontSize: 24,
              ),
            );
            return true;
          },
        ),
      );
      print("Finished delayed effect loop fadein");
    });
    comp.textRenderer = TextPaint(
      style: TextStyle(
        color: targetFilterColor,
        fontSize: 24,
      ),
    );
    sub.cancel();
  }

  Future<void> createTextFadeOut(SimText st, SimTextComponent comp) async {
    print("Reached create text fade in");
    final originalFilterColor = st.filterColor;
    final originalBackgroundColor = st.backgroundColor;
    /* if (st.fadeOut > 0) {
      comp.textRenderer = TextPaint(
        style: TextStyle(
          color: originalFilterColor.withOpacity(1),
          fontSize: 24,
          backgroundColor: originalBackgroundColor.opacity == 0 ? Colors.transparent : originalBackgroundColor.withOpacity(0),
        ),
      );
    } */
    bool active = true;
    final sub = instanceStream.stream.listen((event) {
      if (event == "detached") {
        active = false;
      }
    });
    // will fade in to the set opacity value not 1
    await Future.delayed(Duration(milliseconds: ((st.fadeInWhen + st.fadeInDuration + st.fadeOutWhen + 0.1) * 1000).toInt()), () async {
      print("Reached fade out delayed effect");
      await Future.doWhile(
        () => Future.delayed(
          const Duration(milliseconds: shapeOpacityUpdateInterval),
          () async {
            if (!active) return false;
            final tStyle = (comp.textRenderer as TextPaint).style;
            print("Reached delayed effect loop");
            if (tStyle.color!.opacity == 0) {
              return false;
            }
            final postOpacity = tStyle.color!.opacity - (shapeOpacityUpdateInterval / (1000 * st.fadeOutDuration));
            if (postOpacity <= 0) {
              comp.textRenderer = TextPaint(
                style: TextStyle(
                  color: originalFilterColor.withOpacity(0),
                  fontSize: 24,
                  backgroundColor: originalBackgroundColor.withOpacity(0),
                ),
              );
              return false;
            }
            comp.textRenderer = TextPaint(
              style: TextStyle(
                color: originalFilterColor.withOpacity(postOpacity),
                fontSize: 24,
                backgroundColor: originalBackgroundColor.withOpacity(originalBackgroundColor.opacity == 0 ? 0 : postOpacity),
              ),
            );
            return true;
          },
        ),
      );
      print("Finished delayed effect loop fadeout");
    });
    comp.textRenderer = TextPaint(
      style: TextStyle(
        color: originalFilterColor.withOpacity(0),
        fontSize: 24,
        backgroundColor: originalBackgroundColor.withOpacity(0),
      ),
    );
    sub.cancel();
  }

  Future<void> createTimerFadeOut(SimTimer st, SimTextComponent comp) async {
    print("Reached create text fade in");
    final originalFilterColor = st.filterColor;
    /* if (st.fadeOut > 0) {
      comp.textRenderer = TextPaint(
        style: TextStyle(
          color: originalFilterColor.withOpacity(1),
          fontSize: 24,
          backgroundColor: originalBackgroundColor.opacity == 0 ? Colors.transparent : originalBackgroundColor.withOpacity(0),
        ),
      );
    } */
    bool active = true;
    final sub = instanceStream.stream.listen((event) {
      if (event == "detached") {
        active = false;
      }
    });
    // will fade in to the set opacity value not 1
    await Future.delayed(Duration(milliseconds: ((st.fadeInWhen + st.fadeInDuration + st.fadeOutWhen + 0.1) * 1000).toInt()), () async {
      print("Reached fade out delayed effect");
      await Future.doWhile(
        () => Future.delayed(
          const Duration(milliseconds: shapeOpacityUpdateInterval),
          () async {
            if (!active) return false;
            final tStyle = (comp.textRenderer as TextPaint).style;
            print("Reached delayed effect loop");
            if (tStyle.color!.opacity == 0) {
              return false;
            }
            final postOpacity = tStyle.color!.opacity - (shapeOpacityUpdateInterval / (1000 * st.fadeOutDuration));
            if (postOpacity <= 0) {
              comp.textRenderer = TextPaint(
                style: TextStyle(
                  color: originalFilterColor.withOpacity(0),
                  fontSize: 24,
                ),
              );
              return false;
            }
            comp.textRenderer = TextPaint(
              style: TextStyle(
                color: originalFilterColor.withOpacity(postOpacity),
                fontSize: 24,
              ),
            );
            return true;
          },
        ),
      );
      print("Finished delayed effect loop fadeout");
    });
    comp.textRenderer = TextPaint(
      style: TextStyle(
        color: originalFilterColor.withOpacity(0),
        fontSize: 24,
      ),
    );
    sub.cancel();
  }

  List<SimPerson> findSyncedPeople(String id) {
    List<SimPerson> synced = [];
    for (final loc in sim.locations) {
      for (final person in loc.people) {
        if (person.syncVariable == id) {
          synced.add(person);
        }
      }
    }
    return synced;
  }

  (List<SimPerson>, List<SimImage>) findSyncedPeopleAndImages(String id) {
    List<SimPerson> syncedPeople = [];
    List<SimImage> syncedImages = [];
    for (final loc in sim.locations) {
      for (final person in loc.people) {
        print(
            "Checking ID $id against person ${person.id} for sync vars ${person.syncVariable} at location ${loc.id} (${person.syncVariable} == $id || ${person.syncVariable} == ${id}___${loc.id})");
        if (person.syncVariable == id || person.syncVariable == id + "___" + loc.id) {
          syncedPeople.add(person);
        }
      }
      for (final img in loc.images) {
        print(
            "Checking ID $id against image ${img.id} for sync vars ${img.syncVariable} at location ${loc.id} (${img.syncVariable} == $id || ${img.syncVariable} == ${id}__${loc.id})");
        if (img.syncVariable == id || img.syncVariable == id + "___" + loc.id) {
          syncedImages.add(img);
        }
      }
    }
    return (syncedPeople, syncedImages);
  }

  Future<void> redraw() async {
    // remove(background);
    // if (background.parent != null) {
    // }
    if (_simController.currentSim.value == null) {
      print("Null current sim in game. Returning");
      return;
    }
    print("Size: $size & canvas size: $canvasSize");
    print("Image scale: ${_simController.currentSim.value!.locations[_simController.currentLocation.value].imageScale}");
    print("Sim Dimensions: ${sim.width} x ${sim.height}");
    print("Offset: ${_simController.currentSim.value!.locations[_simController.currentLocation.value].imageOffset}");
    // setBackground();
    removeAll(children);
    // removeAll(children.skip(children.isEmpty || dontSkipBg ? 0 : 1));
    // NOTE: image might not be provided. The background is a color only
    Image? bg;
    if (Flame.images.containsKey("background-$id-${_simController.currentLocation.value.toString()}")) {
      bg = Flame.images.fromCache("background-$id-${_simController.currentLocation.value.toString()}");
    } else {
      print("Decoding Imaage ");
      if (sim.locations[_simController.currentLocation.value].image.isNotEmpty) {
        print("Decoding Imaage 3");
        bg = await Flame.images.fromBase64(
            "background-$id-${_simController.currentLocation.value.toString()}", sim.locations[_simController.currentLocation.value].image);
      }
    }
    bgImage = bg;
    if (bg != null) {
      print("Background size: ${(size.y / bg.height) * bg.width} x ${(size.x / bg.width) * bg.height}");
    } else {
      print(
          "Background is null and size is ${size.x} x ${size.y} and color ${_simController.currentSim.value!.locations[_simController.currentLocation.value].color}");
    }
    final brightness = _simController.currentSim.value!.locations[_simController.currentLocation.value].imageBrightness;
    PositionComponent background;
    if (bg == null) {
      background = BackgroundColorComponent(
        color: Color(int.tryParse(_simController.currentSim.value!.locations[_simController.currentLocation.value].color, radix: 16) ?? 0xFF000000),
        onDragged: (comp, _) {},
        onTapped: (comp, event) {
          if (playMode) return;
          if (_simController.newMask.value != null) {
            print("x: ${event.canvasPosition.x} / ${size.x}, y: ${event.canvasPosition.y} / ${size.y}");
            // _simController.newMask.value!.coordinates
            //     .add(Coordinate(event.canvasPosition.x / size.x, (event.canvasPosition.y /* + backgroundVerticalOffset */) / size.y));
            /* 
            NOTE: using the sim width and height instead of canvas size (although it won't make a difference since the size is set on game resize)
                  to maintain consistency if for some reason we decided to remove the auto update of the sim width and height on game resize
          */
            _simController.newMask.value!.coordinates
                .add(Coordinate(event.canvasPosition.x / sim.width, (event.canvasPosition.y /* + backgroundVerticalOffset */) / sim.height));
            _simController.newMask.refresh();
            return;
          }
          if (_simController.selectedSimObjectIndex.value != -1 && _simController.selectedType.value != null) {
            _simController.selectedSimObjectIndex.value = -1;
            _simController.selectedType.value = null;
            _simController.currentSim.refresh();
            return;
          }
        },
      )
        ..size = size
        ..priority = 0;
    } else {
      background = SimSpriteComponent(
        onDragged: (comp, event) {},
        onTapped: (comp, event) {
          if (playMode) return;
          if (_simController.newMask.value != null) {
            print("x: ${event.canvasPosition.x} / ${size.x}, y: ${event.canvasPosition.y} / ${size.y}");
            // _simController.newMask.value!.coordinates
            //     .add(Coordinate(event.canvasPosition.x / size.x, (event.canvasPosition.y /* + backgroundVerticalOffset */) / size.y));
            /* 
              NOTE: using the sim width and height instead of canvas size (although it won't make a difference since the size is set on game resize)
                    to maintain consistency if for some reason we decided to remove the auto update of the sim width and height on game resize
            */
            _simController.newMask.value!.coordinates
                .add(Coordinate(event.canvasPosition.x / sim.width, (event.canvasPosition.y /* + backgroundVerticalOffset */) / sim.height));
            _simController.newMask.refresh();
            return;
          }
          if (_simController.selectedSimObjectIndex.value != -1 && _simController.selectedType.value != null) {
            _simController.selectedSimObjectIndex.value = -1;
            _simController.selectedType.value = null;
            _simController.currentSim.refresh();
            return;
          }
        },
        onDoubleTap: (comp, event) {
          if (playMode) return;
          if (_simController.newMask.value != null) return;
          if (_simController.selectedSimObjectIndex.value == -1 && _simController.selectedType.value == null) {
            // add new text
            sim.locations[_simController.currentLocation.value].texts
                .add(SimText(x: event.canvasPosition.x, y: event.canvasPosition.y, text: "New Text"));
            _simController.currentSim.refresh();
          }
        },
      )
        ..sprite = Sprite(bg)
        // ..size = Vector2((size.y / bg.height) * bg.width, (size.x / bg.width) * bg.height)
        // ..size = Vector2(sim.width, sim.height)
        // ..size = bg.width > bg.height? Vector2(size.x, (size.x / bg.width) * bg.height) : Vector2((size.y / bg.height) * bg.width, size.y)
        // ..size = Vector2(size.x, size.y)
        // ..size = Vector2(size.x, (size.x / bg.width) * bg.height)
        // ..size = Vector2(max(bg.width.toDouble(), size.x), max(bg.height.toDouble(), (size.x / bg.width) * bg.height))
        ..size = Vector2(
          bg.width.toDouble() * sim.locations[_simController.currentLocation.value].imageScale,
          bg.height.toDouble() * sim.locations[_simController.currentLocation.value].imageScale,
        )
        // ..size = Vector2(size.x, size.y)
        // ..size = Vector2(max(size.x, (size.y / bg.height) * size.x), size.y)
        ..angle = sim.locations[_simController.currentLocation.value].imageRotation * pi / 180
        ..anchor = Anchor.center
        ..position = Vector2(size.x / 2, size.y / 2)
        ..decorator.addLast(
          frendering.PaintDecorator.tint(
            brightness < 0 ? Colors.black.withOpacity(brightness / -20) : Colors.white.withOpacity(brightness / 20),
          ),
        )
        // ..position = Vector2(
        //   (size.x / 2) -
        //       (2 *
        //           size.x *
        //           sim.locations[_simController.currentLocation.value].imageOffset
        //               .dx /* *
        //           sim.locations[_simController.currentLocation.value].imageScale */
        //           /
        //           sim.width),
        //   (size.y / 2) -
        //       (2 *
        //           size.y *
        //           sim.locations[_simController.currentLocation.value].imageOffset
        //               .dy /* *
        //           sim.locations[_simController.currentLocation.value].imageScale */
        //           /
        //           sim.height),
        // )
        /* ..scale = Vector2(
          sim.locations[_simController.currentLocation.value].imageScale,
          sim.locations[_simController.currentLocation.value].imageScale,
          // sim.locations[_simController.currentLocation.value].imageScale * (playMode ? sim.height / size.y : 1),
        ) */
        ..priority = 0;
    }

    // if (background.parent != null) {
    //   background.parent = null;
    // }

    // if (playMode) {
    //   print("Redrawing sim in play mode: ${_simController.currentSim.value!}");
    // }
    // final sim = _simController.currentSim.value!;
    // final sim = playMode ? _simController.currentSim.value!.copy() : _simController.currentSim.value!;
    // if (playMode) {
    //   print("Sim masks in playmode: ${sim.masks}");
    // }
    // final masks = simMasks.map((mask) {
    final masks = sim.masks.map((mask) {
      // print("++++++++++++++++++ Mask: ${mask.id} has coordinates: ${mask.coordinates}");
      bool needsParsing = mask.needsParsing();
      if (needsParsing) {
        // print("Mask ${mask.id} Needs parsing ${mask.coordinates}");
        // print("Mask that needs parsing BEFORE: $mask");
        // consider adding background vertical offset
        // mask.coordinates = mask.coordinates.map((c) => Coordinate(c.x * size.x, c.y * size.y)).toList();
        mask.coordinates = mask.coordinates
            .map((c) => Coordinate(
                  c.x * sim.width * playModeCompensator.x,
                  c.y * sim.height * playModeCompensator.y,
                ))
            .toList();
        // mask.coordinates = mask.coordinates.map((c) => Coordinate(c.x * size.x, c.y * (size.x / bgImage.width) * bgImage.height)).toList();
        // print("Mask AFTER: $mask");
      } /*  else {
        print("Mask ${mask.id} does NOT need parsing");
      } */
      return mask;
    });
    // print("Redraw Masks: $masks");

    // backgroundVerticalOffset = (size.y - ((size.x / bg.width) * bg.height)) / 2;
    // print("backgroundVerticalOffset: $backgroundVerticalOffset");
    // backgroundVerticalOffset = clampDouble(backgroundVerticalOffset, 0, double.infinity);
    // print("BACKGROUND VERTICAL OFFSET: $backgroundVerticalOffset");
    /* if (playMode) {
      background.position.y += (size.y - ((size.x / bg.width) * sim.height)) / 2;
    } */
    await add(background);

    if (_simController.newMask.value != null) {
      final newMaskIndicator = RectangleComponent(
        anchor: Anchor.topLeft,
        position: Vector2(0, 0),
        size: Vector2(size.x, size.y),
        priority: -1000,
        paint: highlightPaint,
      );
      add(newMaskIndicator);
    }

    if (_simController.newMask.value != null && _simController.newMask.value!.coordinates.isNotEmpty) {
      ShapeComponent pathComponent;
      const radius = 4.0;
      // final drawingColor = Colors.white.withOpacity(0.8);
      final drawingColor = Color(int.tryParse(_simController.newMask.value!.color, radix: 16) ?? 0xff000000).withOpacity(newMaskColorOpacity);
      if (_simController.newMask.value!.coordinates.length == 1) {
        pathComponent = CircleComponent(
          anchor: Anchor.center,
          radius: radius,
          position: Vector2(
            (_simController.newMask.value!.coordinates[0].x * size.x) - (radius / 2),
            (_simController.newMask.value!.coordinates[0].y * size.y) - (radius / 2),
          ),
        );
      } else if (_simController.newMask.value!.coordinates.length == 2) {
        pathComponent = LineComponent(
          color: drawingColor,
          start: Vector2(
            (_simController.newMask.value!.coordinates[0].x * size.x),
            (_simController.newMask.value!.coordinates[0].y * size.y),
          ),
          end: Vector2(
            _simController.newMask.value!.coordinates[1].x * size.x,
            _simController.newMask.value!.coordinates[1].y * size.y,
          ),
        )..size = size;
      } else {
        final maskPaint = BasicPalette.black.paint()
          // ..color = Color(int.tryParse(_simController.newMask.value!.color, radix: 16) ?? 0xff000000)
          ..color = drawingColor
          ..style = _simController.newMask.value!.type == MaskType.showWithin ? PaintingStyle.stroke : PaintingStyle.fill
          ..strokeWidth = 3;

        print(_simController.newMask.value!.coordinates.map((coor) => Vector2(coor.x, coor.y)).toList());

        pathComponent = PolygonComponent(
          _simController.newMask.value!.coordinates.map((coor) => Vector2(coor.x * size.x, coor.y * size.y)).toList(),
          paint: maskPaint,
          // size: size,
        );
      }
      // pathComponent.size = size;
      // background.add(pathComponent);
      add(pathComponent);

      if (_simController.newMask.value!.coordinates.length > 1) {
        _simController.newMask.value!.coordinates.forEachIndexed((index, coor) {
          final point = SimShapeComponent(
              onDragged: (comp, _) {
                _simController.newMask.value!.coordinates[index] = Coordinate(comp.x / size.x, comp.y / size.y);
                _simController.newMask.refresh();
              },
              onTapped: (comp, _) {},
              shape: CircleComponent(
                anchor: Anchor.center,
                radius: radius,
              ))
            ..position = Vector2(
              coor.x * size.x,
              coor.y * size.y,
            )
            ..anchor = Anchor.center
            ..size = Vector2.all(radius * 2);
          add(point);
        });
      }
    }

    if (_simController.newMask.value != null ||
        (_simController.selectedSimObjectIndex.value != -1 && _simController.selectedType.value == SimObjectType.mask)) {
      Mask mask;
      if (_simController.newMask.value != null) {
        mask = _simController.newMask.value!;
      } else {
        final masksList = masks.where((mask) => mask.locationId == sim.locations[_simController.currentLocation.value].id).toList();
        if (masksList.isEmpty) {
          return;
        }
        mask = masksList[_simController.selectedSimObjectIndex.value];
      }
      List<SimShapeComponent> vertices = generateMaskEditorVertices(mask);
      addAll(vertices);
      if (_simController.newMask.value != null) {
        return; // stop rendering anything after this point
      }
    }

    sim.locations[_simController.currentLocation.value].sprites.forEachIndexed((index, sprite) {
      // SpriteAnimation.fromAsepriteData(sprite.img, sprite.aseprite!);
      // final anim = SpriteAnimation.fromAsepriteData(sprite.img, sprite.aseprite!);
      if (sprite.hidden) return;
      double largestWidth = 0;
      double largestHeight = 0;
      final scaleDiffWidth = sprite.img.width / sprite.width;
      final scaleDiffHeight = sprite.img.height / sprite.height;
      final animData = SpriteAnimationData(
        sprite.frames.map((frame) {
          if (frame.width > largestWidth) largestWidth = frame.width;
          if (frame.height > largestHeight) largestHeight = frame.height;
          return SpriteAnimationFrameData(
            srcPosition: Vector2(frame.x * scaleDiffWidth, frame.y * scaleDiffHeight),
            srcSize: Vector2(frame.width * scaleDiffWidth, frame.height * scaleDiffHeight),
            stepTime: /* 0.05 */ 1 / (sprite.framerate * sprite.speed) /* (1 / sprite.framerate) */,
          );
        }).toList(),
        loop: true,
      );
      largestWidth *= scaleDiffWidth;
      largestHeight *= scaleDiffHeight;
      final anim = SpriteAnimation.fromFrameData(sprite.img, animData);
      final scaleFactor = sprite.scaleFactor /* 1 */ /* 4 */;
      final mainWidth = largestWidth * sprite.scale * sprite.widthScale * scaleFactor;
      final mainHeight = largestHeight * sprite.scale * sprite.heightScale * scaleFactor;
      print("Adding masks: ${masks.where((mask) => sprite.maskIds.contains(mask.id)).map((e) => e.id).toList()} to sprite ${sprite.id}");
      // print("Sprite: ${sprite.id} largest width: $largestWidth, largest height: $largestHeight mainWidth: $mainWidth, mainHeight: $mainHeight");
      // print("Position of sprite $index: ${Vector2(size.x * sprite.x, size.y * sprite.y)} and size ${Vector2(mainWidth, initialSize * mainHeight)}");
      final ac = SimSpriteAnimationComponent(
        playMode: playMode && !sprite.movable,
        simSize: Vector2(sim.width, sim.height),
        rotation: sprite.rotation * (pi / 180),
        // simSize: Vector2(size.x, size.y),
        onDragged: (
          simComponent,
        ) {
          if (playMode && !sprite.movable) return;
          // print("Absolute: (${simComponent.absolutePosition.x}, ${simComponent.absolutePosition.y}), Loc: (${simComponent.x}, ${simComponent.y})");

          // NOTE: do NOT use variable "sim" in the next line. It causes the sprites to duplicate when you drag them.
          _simController.currentSim.value!.locations[_simController.currentLocation.value].sprites.firstWhere((s) => s.id == sprite.id)
            ..x = simComponent.x / size.x
            ..y = simComponent.y / size.y;
          // _simController.currentSim.refresh();
          redraw();
          // debounceRedraw();
        },
        onTapped: (simComponent) {
          print("Sprite Tapped: ${sprite.id}");
          if (playMode) return;
          if (_simController.newMask.value != null) return;
          if (_simController.selectedType.value == SimObjectType.sprite && _simController.selectedSimObjectIndex.value == index) {
            deselectAllObjects();
          } else {
            //deselect all objects
            deselectAllObjects();
            //then select sprite object
            _simController.selectedType.value = SimObjectType.sprite;
            _simController.selectedSimObjectIndex.value = index;
            _clipboardController.clearSelection();
            _clipboardController.selectedObjects.refresh();
            _clipboardController.selectedObjects.add(sprite);
          }
          _simController.currentSim.refresh();
        },
        masks: masks.where((mask) => sprite.maskIds.contains(mask.id)).toList(),
      )
        ..animation = anim
        ..position = Vector2(size.x * sprite.x, size.y * sprite.y /* - (playMode ? backgroundVerticalOffset / 2 : 0) */)
        ..angle = sprite.rotation * pi / 180
        ..size = Vector2(mainWidth * playModeCompensator.x, /* initialSize * */ mainHeight * playModeCompensator.y)
        // ..size = Vector2(150, 150)
        ..anchor = Anchor.center
        ..tint(sprite.filterColor)
        ..setOpacity(sprite.opacity)
        ..decorator.addLast(frendering.PaintDecorator.blur(sprite.blur))
        ..selected = _simController.selectedType.value == SimObjectType.sprite && _simController.selectedSimObjectIndex.value == index
        ..priority = (sprite.priority + 1) *
            (_simController.selectedType.value == SimObjectType.sprite && _simController.selectedSimObjectIndex.value == index ? 9000 : 1)
        ..playing = sprite.playing;

      ac.paint.filterQuality = ui.FilterQuality.high;
      if (sprite.mirrorX) {
        // ac.scale.multiply(Vector2(-1, 1));
        // ac.flipVerticallyAroundCenter();
        ac.flipVertically();
      }
      if (sprite.mirrorY) {
        // ac.scale.multiply(Vector2(1, -1));
        // ac.flipHorizontallyAroundCenter();
        ac.flipHorizontally();
      }

      if (_simController.selectedSimObjectIndex.value == index && _simController.selectedType.value == SimObjectType.sprite) {
        /* final selectedContainer = RectangleComponent(
          position: Vector2(0, 0),
          size: ac.size,
          anchor: Anchor.topLeft,
          paint: highlightPaint,
        );
        final widenComponent = buildWidenComponent(ac, sprite);
        final rotationComponent = buildRotationComponent(ac, sprite);
        final removeComponent = buildRemoveComponent(ac, sprite);
        selectedContainer.addAll([widenComponent, rotationComponent, removeComponent]); */
        // ac.add(selectedContainer);
        selectedComponent = ac;
      }
      if (playMode) {
        if (sprite.fadeInWhen > 0 && sprite.trigger.isNotEmpty) {
          print("Sprite ${sprite.id} & Triggered: ${itemsTriggeredOnce["sprites"]}");
          switch (sprite.trigger) {
            case "scenario":
              if (simTime > sprite.fadeInWhen) {
                return;
              }
              break;
            case "state":
              if (stateTime > sprite.fadeInWhen) {
                return;
              }
              break;
            case "location":
              if (locationTime > sprite.fadeInWhen) {
                return;
              }
              break;
            default:
              print("Unknown trigger: ${sprite.trigger}");
              return;
          }
          if (!sprite.triggerOnce || !itemsTriggeredOnce["sprites"]!["fadeIn"]!.contains(sprite.id)) {
            ac.opacity = 0;
            final controller = DelayedEffectController(EffectController(duration: sprite.fadeInDuration + 0.1), delay: sprite.fadeInWhen + 0.1);
            ac.add(OpacityEffect.fadeIn(controller, onComplete: () {
              itemsTriggeredOnce["sprites"]!["fadeIn"]!.add(sprite.id);
            }));
          } else {
            // nothing to do here. opacity isn't set to zero depending on another set of hidden list
          }
        }
        if (sprite.fadeOutWhen > 0 && sprite.trigger.isNotEmpty) {
          switch (sprite.trigger) {
            case "scenario":
              if (simTime > sprite.fadeOutWhen) {
                return;
              }
              break;
            case "state":
              if (stateTime > sprite.fadeOutWhen) {
                return;
              }
              break;
            case "location":
              if (locationTime > sprite.fadeOutWhen) {
                return;
              }
              break;
            default:
              print("Unknown trigger: ${sprite.trigger}");
              return;
          }
          if (!sprite.triggerOnce || !itemsTriggeredOnce["sprites"]!["fadeOut"]!.contains(sprite.id)) {
            final controller = DelayedEffectController(EffectController(duration: sprite.fadeOutDuration + 0.1),
                delay: sprite.fadeInWhen + sprite.fadeInDuration + sprite.fadeOutWhen + 0.1);
            ac.add(OpacityEffect.fadeOut(
              controller,
              onComplete: () {
                itemsTriggeredOnce["sprites"]!["fadeOut"]!.add(sprite.id);
              },
            ));
          } else {
            // prevent the sprite from rendering as it has already been faded out
            return;
          }
        }
      }

      add(ac);
      // ac.animation
    });

    sim.locations[_simController.currentLocation.value].images.forEachIndexed((index, img) {
      print("Image ${img.id} has sync vars: ${img.syncVariable}");
      if (img.hidden) return;
      final ic = SimSpriteComponent(
        playMode: playMode && !img.movable,
        onDragged: (comp, event) {
          if (playMode && !img.movable) return;
          comp.position += event.delta;
          (findByKeyName("controls") as PositionComponent?)?.position += event.delta;
        },
        onDragComplete: (comp) {
          img.x = comp.x;
          img.y = comp.y;
        },
        onTapped: (comp, event) {
          print("Image ${img.id} tapped and play mode is $playMode with direction to ${img.to}");
          if (playMode) {
            // TODO: need to check if checking for the visibility of the image is necessary
            // Or maybe the order of execution should be changed

            if (img.to.isNotEmpty && !hiddenImages.contains(img.id)) {
              _simController.jumpToLocation(img.to);
              redraw();
              return "jump";
            }
            final (syncedPeople, syncedImages) = findSyncedPeopleAndImages(img.id);
            print(
                "For image ${img.id}: Synced people: ${syncedPeople.map((p) => p.id).toList()} and synced images: ${syncedImages.map((i) => i.id).toList()}");
            if (img.clickToToggle && !hiddenImages.contains(img.id)) {
              hiddenImages.add(img.id);
              if (syncedPeople.isNotEmpty) {
                for (final p in syncedPeople) {
                  hiddenPeople.add(p.id);
                }
              }
              if (syncedImages.isNotEmpty) {
                for (final i in syncedImages) {
                  hiddenImages.add(i.id);
                }
              }
              redraw();
              return "hide";
            }
            if (img.clickToToggle && hiddenImages.contains(img.id)) {
              hiddenImages.remove(img.id);
              if (syncedPeople.isNotEmpty) {
                for (final p in syncedPeople) {
                  hiddenPeople.remove(p.id);
                }
              }
              if (syncedImages.isNotEmpty) {
                for (final i in syncedImages) {
                  hiddenImages.remove(i.id);
                }
              }
              redraw();
              return "show";
            }
            return "none";
          }
          if (_simController.newMask.value != null) return "new mask null";
          if (_simController.selectedType.value == SimObjectType.image && _simController.selectedSimObjectIndex.value == index) {
            deselectAllObjects();
          } else {
            //deselect all objects
            deselectAllObjects();
            //then select image object
            _simController.selectedType.value = SimObjectType.image;
            _simController.selectedSimObjectIndex.value = index;
            _clipboardController.clearSelection();
            _clipboardController.selectedObjects.refresh();
            _clipboardController.selectedObjects.add(img);
          }
          _simController.currentSim.refresh();
          return "selected";
        },
        masks: masks.where((mask) => img.maskIds.contains(mask.id)).toList(),
      )
        ..sprite = Sprite(img.img)
        ..position = Vector2(img.x * size.x / sim.width, img.y * size.y / sim.height)
        ..angle = img.rotation * pi / 180
        ..anchor = Anchor.center
        ..size = Vector2(
          img.width * img.scale * img.widthScale * playModeCompensator.x,
          img.height * img.scale * img.heightScale * playModeCompensator.y,
        )
        ..opacity = hiddenImages.contains(img.id) ? 0 : img.opacity
        ..decorator.addLast(frendering.PaintDecorator.blur(img.blur))
        ..decorator.addLast(frendering.PaintDecorator.tint(img.filterColor))
        // ..scale = Vector2(img.scale, img.scale)
        ..priority = (img.priority + 1) *
            (_simController.selectedType.value == SimObjectType.image && _simController.selectedSimObjectIndex.value == index ? 9000 : 1);

      if (img.mirrorX) {
        ic.flipVertically();
      }
      if (img.mirrorY) {
        ic.flipHorizontally();
      }

      if (_simController.selectedType.value == SimObjectType.image && _simController.selectedSimObjectIndex.value == index) {
        final selectedContainer = RectangleComponent(
          position: Vector2(0, 0),
          size: ic.size,
          anchor: Anchor.topLeft,
          paint: highlightPaint,
        );
        final widenComponent = buildWidenComponent(ic, img);
        final rotationComponent = buildRotationComponent(ic, img);
        final removeComponent = buildRemoveComponent(ic, img);
        selectedContainer.addAll([widenComponent, rotationComponent, removeComponent]);
        // ic.add(selectedContainer);
        selectedComponent = ic;
      }
      if (playMode) {
        if (img.fadeInWhen > 0 && img.trigger.isNotEmpty) {
          print("Image ${img.id} & Triggered: ${itemsTriggeredOnce["images"]}");
          switch (img.trigger) {
            case "scenario":
              if (simTime > img.fadeInWhen) {
                return;
              }
              break;
            case "state":
              if (stateTime > img.fadeInWhen) {
                return;
              }
              break;
            case "location":
              if (locationTime > img.fadeInWhen) {
                return;
              }
              break;
            default:
              print("Unknown trigger: ${img.trigger}");
              return;
          }
          if (!img.triggerOnce || !itemsTriggeredOnce["images"]!["fadeIn"]!.contains(img.id)) {
            ic.opacity = 0;
            final controller = DelayedEffectController(EffectController(duration: img.fadeInDuration + 0.1), delay: img.fadeInWhen + 0.1);
            ic.add(OpacityEffect.fadeIn(controller, onComplete: () {
              itemsTriggeredOnce["images"]!["fadeIn"]!.add(img.id);
            }));
          } else {
            hiddenImages.remove(img.id);
          }
        }
        if (img.fadeOutWhen > 0 && img.trigger.isNotEmpty && img.trigger.isNotEmpty) {
          switch (img.trigger) {
            case "scenario":
              if (simTime > img.fadeOutWhen) {
                return;
              }
              break;
            case "state":
              if (stateTime > img.fadeOutWhen) {
                return;
              }
              break;
            case "location":
              if (locationTime > img.fadeOutWhen) {
                return;
              }
              break;
            default:
              print("Unknown trigger: ${img.trigger}");
              return;
          }
          if (!img.triggerOnce || !itemsTriggeredOnce["images"]!["fadeOut"]!.contains(img.id)) {
            final controller = DelayedEffectController(EffectController(duration: img.fadeOutDuration + 0.1),
                delay: img.fadeInWhen + img.fadeInDuration + img.fadeOutWhen + 0.1);
            ic.add(OpacityEffect.fadeOut(controller, onComplete: () {
              itemsTriggeredOnce["images"]!["fadeOut"]!.add(img.id);
            }));
          } else {
            // prevents the image from rendering as it has already been faded
            return;
          }
        }
      }
      add(ic);
    });

    sim.locations[_simController.currentLocation.value].people.forEachIndexed((index, person) {
      print("Person ${person.id} has sync vars: ${person.syncVariable}}");
      if (person.hidden) return;
      final personImage = peopleImages["${peopleToAssetMapping[person.type]}-${person.posture}"]!;
      final initialSize = personImage.width >= personImage.height
          ? clampDouble(personImage.width.toDouble(), size.x / 6, size.x / 3)
          : clampDouble(personImage.height.toDouble(), size.y / 6, size.y / 3);
      final pc = SimSpriteComponent(
        playMode: playMode && !person.movable,
        onDragged: (comp, event) {
          if (playMode && !person.movable) return;
          comp.position += event.delta;
          (findByKeyName("controls") as PositionComponent?)?.position += event.delta;
        },
        onDragComplete: (comp) {
          if (playMode) return;
          person.x = comp.x;
          person.y = comp.y;
          redraw();
        },
        onTapped: (comp, event) {
          if (playMode) {
            // TODO: need to check if checking for the visibility of the image is necessary
            final (syncedPeople, syncedImages) = findSyncedPeopleAndImages(person.id);
            print("For victim ${person.id} Synced people: $syncedPeople, Synced images: $syncedImages");
            if (person.clickToToggle && !hiddenPeople.contains(person.id)) {
              hiddenPeople.add(person.id);
              if (syncedPeople.isNotEmpty) {
                for (final p in syncedPeople) {
                  hiddenPeople.add(p.id);
                }
              }
              if (syncedImages.isNotEmpty) {
                for (final i in syncedImages) {
                  hiddenImages.add(i.id);
                }
              }
              redraw();
              return "hide";
            }
            if (person.clickToToggle && hiddenPeople.contains(person.id)) {
              hiddenPeople.remove(person.id);
              if (syncedPeople.isNotEmpty) {
                for (final p in syncedPeople) {
                  hiddenPeople.remove(p.id);
                }
              }
              if (syncedImages.isNotEmpty) {
                for (final i in syncedImages) {
                  hiddenImages.remove(i.id);
                }
              }
              redraw();
              return "show";
            }
            // Or maybe the order of execution should be changed
            return null;
          }
          if (_simController.newMask.value != null) return "new mask null";
          if (_simController.selectedType.value == SimObjectType.person && _simController.selectedSimObjectIndex.value == index) {
            deselectAllObjects();
          } else {
            //deselect all objects
            deselectAllObjects();
            //then select people object
            _simController.selectedType.value = SimObjectType.person;
            _simController.selectedSimObjectIndex.value = index;
            _clipboardController.clearSelection();
            _clipboardController.selectedObjects.refresh();
            _clipboardController.selectedObjects.add(person);
          }
          _simController.currentSim.refresh();
          return "selected";
        },
        masks: masks.where((mask) => person.maskIds.contains(mask.id)).toList(),
      )
        ..sprite = Sprite(personImage)
        ..position = Vector2(person.x * size.x / sim.width, person.y * size.y / sim.height)
        ..angle = person.rotation * pi / 180
        ..anchor = Anchor.center
        ..size = Vector2(initialSize * person.scale * person.widthScale,
            initialSize * (personImage.height / personImage.width) * person.scale * person.heightScale)
        ..opacity = hiddenPeople.contains(person.id) ? 0 : person.opacity
        ..decorator.addLast(frendering.PaintDecorator.blur(person.blur))
        ..decorator.addLast(frendering.PaintDecorator.tint(person.filterColor))
        ..scale = Vector2(person.scale, person.scale)
        ..priority = (person.priority + 1) *
            (_simController.selectedType.value == SimObjectType.image && _simController.selectedSimObjectIndex.value == index ? 9000 : 1);

      if (person.mirrorX) {
        pc.flipVertically();
      }
      if (person.mirrorY) {
        pc.flipHorizontally();
      }

      if (_simController.selectedType.value == SimObjectType.person && _simController.selectedSimObjectIndex.value == index) {
        final selectedContainer = RectangleComponent(
          position: Vector2(0, 0),
          size: pc.size,
          anchor: Anchor.topLeft,
          paint: highlightPaint,
        );
        final widenComponent = buildWidenComponent(pc, person);
        final rotationComponent = buildRotationComponent(pc, person);
        final removeComponent = buildRemoveComponent(pc, person);
        selectedContainer.addAll([widenComponent, rotationComponent, removeComponent]);
        // pc.add(selectedContainer);
        selectedComponent = pc;
      }
      if (playMode) {
        print("Person ${person.id} & Triggered: ${itemsTriggeredOnce["people"]}");
        if (person.fadeInWhen > 0 && person.trigger.isNotEmpty) {
          switch (person.trigger) {
            case "scenario":
              if (simTime > person.fadeInWhen) {
                return;
              }
              break;
            case "state":
              if (stateTime > person.fadeInWhen) {
                return;
              }
              break;
            case "location":
              if (locationTime > person.fadeInWhen) {
                return;
              }
              break;
            default:
              print("Unknown trigger: ${person.trigger}");
              return;
          }
          if (!person.triggerOnce || !itemsTriggeredOnce["people"]!["fadeIn"]!.contains(person.id)) {
            print("Setting Person Component opacity to 0");
            pc.opacity = 0;
            final controller = DelayedEffectController(EffectController(duration: person.fadeInDuration + 0.1), delay: person.fadeInWhen + 0.1);
            pc.add(OpacityEffect.fadeIn(controller, onComplete: () {
              itemsTriggeredOnce["people"]!["fadeIn"]!.add(person.id);
            }));
          } else {
            hiddenPeople.remove(person.id);
          }
        }
        if (person.fadeOutWhen > 0 && person.trigger.isNotEmpty) {
          switch (person.trigger) {
            case "scenario":
              if (simTime > person.fadeOutWhen) {
                return;
              }
              break;
            case "state":
              if (stateTime > person.fadeOutWhen) {
                return;
              }
              break;
            case "location":
              if (locationTime > person.fadeOutWhen) {
                return;
              }
              break;
            default:
              print("Unknown trigger: ${person.trigger}");
              return;
          }
          if (!person.triggerOnce || !itemsTriggeredOnce["people"]!["fadeOut"]!.contains(person.id)) {
            final controller = DelayedEffectController(EffectController(duration: person.fadeOutDuration + 0.1),
                delay: person.fadeInWhen + person.fadeInDuration + person.fadeOutWhen + 0.1);
            pc.add(OpacityEffect.fadeOut(controller, onComplete: () {
              itemsTriggeredOnce["people"]!["fadeOut"]!.add(person.id);
            }));
          } else {
            // prevents the person from rendering as it has already been faded
            return;
            // hiddenPeople.add(person.id);
          }
        }
      }
      add(pc);
    });

    sim.locations[_simController.currentLocation.value].texts.forEachIndexed((index, text) {
      if (text.hidden) return;
      final tc = SimTextComponent(
        playMode: playMode && !text.movable,
        onDragged: (comp) {
          if (playMode && !text.movable) return;
          sim.locations[_simController.currentLocation.value].texts[index].x = comp.x;
          sim.locations[_simController.currentLocation.value].texts[index].y = comp.y;
          (findByKeyName("controls") as PositionComponent?)?.position = comp.position;
        },
        onTapped: (comp) {
          if (playMode) return;
          if (_simController.newMask.value != null) return;
          if (_simController.selectedType.value == SimObjectType.text && _simController.selectedSimObjectIndex.value == index) {
            deselectAllObjects();
          } else {
            //deselect all objects
            deselectAllObjects();
            //then select text object
            _simController.selectedType.value = SimObjectType.text;
            _simController.selectedSimObjectIndex.value = index;
            _clipboardController.clearSelection();
            _clipboardController.selectedObjects.refresh();
            _clipboardController.selectedObjects.add(text);
          }
          _simController.currentSim.refresh();
        },
        onDoubleTap: (comp) {
          if (playMode) return;
          // send signal to stream to select text editor focus node
          print("Text double tapped");
          _simController.signalStream.add("focusOnText");
        },
      )
        ..text = text.text
        ..position = Vector2(text.x * size.x / sim.width, text.y * size.y / sim.height)
        ..size = Vector2(
            12 * text.scale * text.widthScale * text.text.length * playModeCompensator.x, 35 * text.scale * text.heightScale * playModeCompensator.y)
        ..angle = text.rotation * pi / 180
        ..anchor = Anchor.center
        ..scale = Vector2(text.widthScale * text.scale, text.heightScale * text.scale)
        ..priority = (text.priority + 1) *
            (_simController.selectedType.value == SimObjectType.text && _simController.selectedSimObjectIndex.value == index ? 9000 : 1)
        ..textRenderer = TextPaint(
          style: TextStyle(
            color: text.filterColor,
            fontSize: 24,
            backgroundColor: text.backgroundColor,
          ),
        );
      // ..decorator.addLast(frendering.PaintDecorator.tint(text.filterColor));
      if (_simController.selectedType.value == SimObjectType.text && _simController.selectedSimObjectIndex.value == index) {
        final selectedContainer = RectangleComponent(
          position: Vector2(0, 0),
          size: tc.size,
          anchor: Anchor.topLeft,
          paint: highlightPaint,
        );
        final rotationComponent = buildRotationComponent(tc, text, multiper: Vector2(3, 1));
        selectedContainer.addAll([/* rotationComponent */]);
        // tc.add(selectedContainer);
        selectedComponent = tc;
      }
      if (playMode) {
        if (text.fadeInWhen > 0 && text.trigger.isNotEmpty) {
          bool shouldCreateFadeIn = true;
          switch (text.trigger) {
            case "scenario":
              if (simTime > text.fadeInWhen) {
                print("Sim time larger than fade in time");
                tc.textRenderer = TextPaint(
                  style: TextStyle(
                    color: text.filterColor.withOpacity(1),
                    fontSize: 24,
                    backgroundColor: text.backgroundColor.withOpacity(1),
                  ),
                );
                shouldCreateFadeIn = false;
              }
              break;
            case "state":
              if (stateTime > text.fadeInWhen) {
                print("State time larger than fade in time");
                tc.textRenderer = TextPaint(
                  style: TextStyle(
                    color: text.filterColor.withOpacity(1),
                    fontSize: 24,
                    backgroundColor: text.backgroundColor.withOpacity(1),
                  ),
                );
                shouldCreateFadeIn = false;
              }
              break;
            case "location":
              if (locationTime > text.fadeInWhen) {
                print("Location time larger than fade in time");
                tc.textRenderer = TextPaint(
                  style: TextStyle(
                    color: text.filterColor.withOpacity(1),
                    fontSize: 24,
                    backgroundColor: text.backgroundColor.withOpacity(1),
                  ),
                );
                shouldCreateFadeIn = false;
              }
              break;
            default:
              print("Unknown trigger: ${text.trigger}");
              return;
          }
          print("""
          sim time: $simTime, statetime: $stateTime, locationtime: $locationTime,
          shape fade in when: ${text.fadeInWhen}, shape trigger: ${text.trigger}
          """);
          if (shouldCreateFadeIn) {
            if (!text.triggerOnce || !itemsTriggeredOnce["texts"]!["fadeIn"]!.contains(text.id)) {
              createTextFadeIn(text, tc).then((_) {
                itemsTriggeredOnce["texts"]!["fadeIn"]!.add(text.id);
              });
            }
          }
        }
        if (text.fadeOutWhen > 0 && text.trigger.isNotEmpty) {
          bool shouldCreateFadeOut = true;
          final totalTime = text.fadeOutWhen + text.fadeInWhen;
          switch (text.trigger) {
            case "scenario":
              if (simTime > totalTime) {
                tc.textRenderer = TextPaint(
                  style: TextStyle(
                    color: text.filterColor.withOpacity(0),
                    fontSize: 24,
                    backgroundColor: text.backgroundColor.withOpacity(0),
                  ),
                );
                shouldCreateFadeOut = false;
              }
              break;
            case "state":
              if (stateTime > totalTime) {
                tc.textRenderer = TextPaint(
                  style: TextStyle(
                    color: text.filterColor.withOpacity(0),
                    fontSize: 24,
                    backgroundColor: text.backgroundColor.withOpacity(0),
                  ),
                );
                shouldCreateFadeOut = false;
              }
              break;
            case "location":
              if (locationTime > totalTime) {
                tc.textRenderer = TextPaint(
                  style: TextStyle(
                    color: text.filterColor.withOpacity(0),
                    fontSize: 24,
                    backgroundColor: text.backgroundColor.withOpacity(0),
                  ),
                );
                shouldCreateFadeOut = false;
              }
              break;
            default:
              print("Unknown trigger: ${text.trigger}");
              return;
          }
          if (shouldCreateFadeOut) {
            if (!text.triggerOnce || !itemsTriggeredOnce["texts"]!["fadeOut"]!.contains(text.id)) {
              createTextFadeOut(text, tc).then((_) {
                itemsTriggeredOnce["texts"]!["fadeOut"]!.add(text.id);
              });
            }
          }
        }
      }
      add(tc);
    });

    sim.locations[_simController.currentLocation.value].shapes.forEachIndexed((index, shape) {
      if (shape.hidden) return;
      print("Shape color: ${shape.filterColor} and opacity: ${shape.opacity}");
      var simShapePaint = BasicPalette.white.paint()
        ..style = PaintingStyle.fill
        ..color = shape.filterColor.withOpacity(shape.opacity);
      SimShapeComponent sc;
      sc = SimShapeComponent(
        playMode: playMode && !shape.movable,
        onDragged: (comp, _) {
          if (playMode && !shape.movable) return;
          final obj = sim.locations[_simController.currentLocation.value].shapes[index];
          print("Shape dragged from ${obj.x}, ${obj.y} to ${comp.x}, ${comp.y} and size: $size, canvas size: $canvasSize");
          sim.locations[_simController.currentLocation.value].shapes[index].x = comp.x;
          sim.locations[_simController.currentLocation.value].shapes[index].y = comp.y;
          // (findByKeyName("controls") as PositionComponent?)?.position = comp.position - Vector2(comp.width / 2, comp.height / 2);
          (findByKeyName("controls") as PositionComponent?)?.position = comp.position;
          // debounceRedraw();
        },
        onTapped: (comp, _) {
          print("Shape tapped");
          if (playMode) {
            print("Shape tapped in play mode");
            return;
          }
          if (_simController.newMask.value != null) return;
          if (_simController.selectedSimObjectIndex.value == index && _simController.selectedType.value == SimObjectType.shape) {
            deselectAllObjects();
          } else {
            //deselect all objects
            deselectAllObjects();
            //then select shape object
            _simController.selectedSimObjectIndex.value = index;
            _simController.selectedType.value = SimObjectType.shape;
            _clipboardController.clearSelection();
            _clipboardController.selectedObjects.refresh();
            _clipboardController.selectedObjects.add(shape);
          }

          _simController.currentSim.refresh();
        },
        shapeObject: shape,
        masks: masks.where((mask) => shape.maskIds.contains(mask.id)).toList(),
      )
        ..position = Vector2(shape.x * size.x / sim.width, shape.y * size.y / sim.height)
        ..angle = shape.rotation * pi / 180
        ..anchor = Anchor.center
        ..size = Vector2(initialSize * shape.scale * shape.widthScale * playModeCompensator.x,
            initialSize * shape.scale * shape.heightScale * playModeCompensator.y)
        ..priority = (shape.priority + 1) *
            (_simController.selectedType.value == SimObjectType.shape && _simController.selectedSimObjectIndex.value == index ? 9000 : 1)
        ..anchor = Anchor.center;

      setShapeFromType(shape, sc, simShapePaint);

      if (_simController.selectedSimObjectIndex.value == index && _simController.selectedType.value == SimObjectType.shape) {
        /* final selectedContainer = RectangleComponent(
          position: Vector2(0, 0),
          size: sc.size,
          anchor: Anchor.topLeft,
          paint: highlightPaint,
        );
        final widenComponent = buildWidenComponent(sc, shape);
        final rotationComponent = buildRotationComponent(sc, shape);
        selectedContainer.addAll([widenComponent, rotationComponent]); */
        print("Shape ${shape.id} has a size of: ${sc.size}} and inner component has size of ${sc.shape?.size}");
        // sc.add(selectedContainer);
        selectedComponent = sc;
      }

      if (playMode) {
        if (shape.fadeInWhen > 0 && shape.trigger.isNotEmpty) {
          bool shouldCreateFadeIn = true;
          switch (shape.trigger) {
            case "scenario":
              if (simTime > shape.fadeInWhen) {
                print("Sim time larger than fade in time");
                sc.shape!.paint.color = sc.shape!.paint.color.withOpacity(1);
                shouldCreateFadeIn = false;
              }
              break;
            case "state":
              if (stateTime > shape.fadeInWhen) {
                print("State time larger than fade in time");
                sc.shape!.paint.color = sc.shape!.paint.color.withOpacity(1);
                shouldCreateFadeIn = false;
              }
              break;
            case "location":
              if (locationTime > shape.fadeInWhen) {
                print("Location time larger than fade in time");
                sc.shape!.paint.color = sc.shape!.paint.color.withOpacity(1);
                shouldCreateFadeIn = false;
              }
              break;
            default:
              print("Unknown trigger: ${shape.trigger}");
              return;
          }
          print("""
          sim time: $simTime, statetime: $stateTime, locationtime: $locationTime,
          shape fade in when: ${shape.fadeInWhen}, shape trigger: ${shape.trigger}
          """);
          if (shouldCreateFadeIn) {
            if (!shape.triggerOnce || !itemsTriggeredOnce["shapes"]!["fadeIn"]!.contains(shape.id)) {
              createShapeFadeIn(shape, sc).then((_) {
                itemsTriggeredOnce["shapes"]!["fadeIn"]!.add(shape.id);
              });
            }
          }
          // sc.shape!.opacity = 0;
          // final controller = DelayedEffectController(EffectController(duration: shape.fadeInDuration + 0.1), delay: shape.fadeInWhen + 0.1);
          // sc.shape!.add(OpacityEffect.fadeIn(controller));
        }
        if (shape.fadeOutWhen > 0 && shape.trigger.isNotEmpty) {
          bool shouldCreateFadeOut = true;
          final totalTime = shape.fadeOutWhen + shape.fadeInWhen;
          switch (shape.trigger) {
            case "scenario":
              if (simTime > totalTime) {
                sc.shape!.paint.color = sc.shape!.paint.color.withOpacity(0);
                shouldCreateFadeOut = false;
              }
              break;
            case "state":
              if (stateTime > totalTime) {
                sc.shape!.paint.color = sc.shape!.paint.color.withOpacity(0);
                shouldCreateFadeOut = false;
              }
              break;
            case "location":
              if (locationTime > totalTime) {
                sc.shape!.paint.color = sc.shape!.paint.color.withOpacity(0);
                shouldCreateFadeOut = false;
              }
              break;
            default:
              print("Unknown trigger: ${shape.trigger}");
              return;
          }
          if (shouldCreateFadeOut) {
            if (!shape.triggerOnce || !itemsTriggeredOnce["shapes"]!["fadeOut"]!.contains(shape.id)) {
              createShapeFadeOut(shape, sc).then((_) {
                itemsTriggeredOnce["shapes"]!["fadeOut"]!.add(shape.id);
              });
            }
          }
          // final controller =
          //     DelayedEffectController(EffectController(duration: shape.fadeOutDuration + 0.1), delay: shape.fadeInWhen + shape.fadeOutWhen + 0.1);
          // sc.shape!.add(OpacityEffect.fadeOut(controller));
        }
      }

      if (shape.mirrorX) {
        sc.flipVertically();
      }
      if (shape.mirrorY) {
        sc.flipHorizontally();
      }

      add(sc);
    });

    sim.locations[_simController.currentLocation.value].jumpers.forEachIndexed((index, jumper) {
      if (jumper.hidden) return;
      var simShapePaint = BasicPalette.white.paint()
        ..style = PaintingStyle.fill
        ..color = jumper.filterColor.withOpacity(jumper.opacity);
      SimShapeComponent jc;
      jc = SimShapeComponent(
        playMode: playMode && !jumper.movable,
        onDragged: (comp, _) {
          if (playMode && !jumper.movable) return;
          sim.locations[_simController.currentLocation.value].jumpers[index].x = comp.x;
          sim.locations[_simController.currentLocation.value].jumpers[index].y = comp.y;
          // (findByKeyName("controls") as PositionComponent?)?.position = comp.position - Vector2(comp.width / 2, comp.height / 2);
          (findByKeyName("controls") as PositionComponent?)?.position = comp.position;
          // debounceRedraw();
        },
        onTapped: (comp, _) {
          print("Shape tapped");
          if (playMode) {
            print("Shape tapped in play mode");
            if (jumper.clickable) {
              // go to destination
              _simController.jumpToLocation(jumper.to);
            }
            return;
          }
          if (_simController.newMask.value != null) return;
          if (jumper.runtimeType == SimLocationJumper) {
            if (_simController.selectedSimObjectIndex.value == index && _simController.selectedType.value == SimObjectType.locationJumper) {
              _simController.selectedSimObjectIndex.value = -1;
              _simController.selectedType.value = null;
              _clipboardController.clearSelection();
              _clipboardController.selectedObjects.refresh();
            } else {
              //deselect all objects
              _simController.selectedType.value = null;
              _simController.selectedSimObjectIndex.value = -1;
              _clipboardController.selectedObjects.refresh();
              //then select jumper object
              _simController.selectedSimObjectIndex.value = index;
              _simController.selectedType.value = SimObjectType.locationJumper;
              _clipboardController.clearSelection();
              _clipboardController.selectedObjects.refresh();
              _clipboardController.selectedObjects.add(jumper);
            }
          }
          _simController.currentSim.refresh();
        },
        shapeObject: jumper,
        masks: masks.where((mask) => jumper.maskIds.contains(mask.id)).toList(),
      )
        ..position = Vector2(jumper.x * size.x / sim.width, jumper.y * size.y / sim.height)
        ..angle = jumper.rotation * pi / 180
        ..anchor = Anchor.center
        ..size = Vector2(initialSize * jumper.scale * jumper.widthScale * playModeCompensator.x,
            initialSize * jumper.scale * jumper.heightScale * playModeCompensator.y)
        ..priority = (jumper.priority + 1) *
            (_simController.selectedType.value == SimObjectType.locationJumper && _simController.selectedSimObjectIndex.value == index ? 9000 : 1)
        ..anchor = Anchor.center;

      setShapeFromType(jumper, jc, simShapePaint);

      if (_simController.selectedSimObjectIndex.value == index && _simController.selectedType.value == SimObjectType.locationJumper) {
        final selectedContainer = RectangleComponent(
          position: Vector2(0, 0),
          size: jc.size,
          anchor: Anchor.topLeft,
          paint: highlightPaint,
        );
        final widenComponent = buildWidenComponent(jc, jumper);
        final rotationComponent = buildRotationComponent(jc, jumper);
        selectedContainer.addAll([widenComponent, rotationComponent]);
        // jc.add(selectedContainer);
        selectedComponent = jc;
      }

      if (playMode) {
        if (jumper.fadeInWhen > 0 && jumper.trigger.isNotEmpty) {
          bool shouldCreateFadeIn = true;
          switch (jumper.trigger) {
            case "scenario":
              if (simTime > jumper.fadeInWhen) {
                jc.shape!.paint.color = jc.shape!.paint.color.withOpacity(1);
                shouldCreateFadeIn = false;
              }
              break;
            case "state":
              if (stateTime > jumper.fadeInWhen) {
                jc.shape!.paint.color = jc.shape!.paint.color.withOpacity(1);
                shouldCreateFadeIn = false;
              }
              break;
            case "location":
              if (locationTime > jumper.fadeInWhen) {
                jc.shape!.paint.color = jc.shape!.paint.color.withOpacity(1);
                shouldCreateFadeIn = false;
              }
              break;
            default:
              print("Unknown trigger: ${jumper.trigger}");
              return;
          }
          // jc.shape!.opacity = 0;
          // final controller = DelayedEffectController(EffectController(duration: jumper.fadeInDuration + 0.1), delay: jumper.fadeInWhen + 0.1);
          // jc.shape!.add(OpacityEffect.fadeIn(controller));
          if (shouldCreateFadeIn) {
            if (!jumper.triggerOnce || !itemsTriggeredOnce["jumpers"]!["fadeIn"]!.contains(jumper.id)) {
              createShapeFadeIn(jumper, jc).then((_) {
                itemsTriggeredOnce["jumpers"]!["fadeIn"]!.add(jumper.id);
              });
            }
          }
        }
        if (jumper.fadeOutWhen > 0 && jumper.trigger.isNotEmpty) {
          bool shouldCreateFadeOut = true;
          switch (jumper.trigger) {
            case "scenario":
              if (simTime > jumper.fadeOutWhen) {
                jc.shape!.paint.color = jc.shape!.paint.color.withOpacity(0);
                shouldCreateFadeOut = false;
              }
              break;
            case "state":
              if (stateTime > jumper.fadeOutWhen) {
                jc.shape!.paint.color = jc.shape!.paint.color.withOpacity(0);
                shouldCreateFadeOut = false;
              }
              break;
            case "location":
              if (locationTime > jumper.fadeOutWhen) {
                jc.shape!.paint.color = jc.shape!.paint.color.withOpacity(0);
                shouldCreateFadeOut = false;
              }
              break;
            default:
              print("Unknown trigger: ${jumper.trigger}");
              return;
          }
          if (shouldCreateFadeOut) {
            if (!jumper.triggerOnce || !itemsTriggeredOnce["jumpers"]!["fadeOut"]!.contains(jumper.id)) {
              createShapeFadeOut(jumper, jc).then((_) {
                itemsTriggeredOnce["jumpers"]!["fadeOut"]!.add(jumper.id);
              });
            }
          }
        }
      }

      if (jumper.mirrorX) {
        jc.flipVertically();
      }
      if (jumper.mirrorY) {
        jc.flipHorizontally();
      }

      add(jc);
    });

    sim.locations[_simController.currentLocation.value].sounds.forEachIndexed((index, sound) async {
      if (sound.hidden) return;
      final audio = AudioPlayer();
      // print("SOUND PATH: ${sound.path}");
      if (sound.path.startsWith("asset://")) {
        await audio.setSourceAsset(sound.path.split("asset://")[1]);
      } else {
        await audio.setSource(DeviceFileSource(sound.path));
      }
      audio.setReleaseMode(sound.loop ? ReleaseMode.loop : ReleaseMode.stop);

      final sc = SimSpriteComponent(
        playMode: playMode,
        onDragged: (comp, event) {
          comp.position += event.delta;
          (findByKeyName("controls") as PositionComponent?)?.position += event.delta;
        },
        onDragComplete: (comp) {
          if (playMode) return;
          sim.locations[_simController.currentLocation.value].sounds[index].x = comp.x;
          sim.locations[_simController.currentLocation.value].sounds[index].y = comp.y;
        },
        onTapped: (comp, event) {
          if (playMode) return;
          if (_simController.newMask.value != null) return;
          /* if (audio.state == PlayerState.playing) {
          return audio.stop();
        } else {
          return audio.resume();
        } */
          if (_simController.selectedSimObjectIndex.value == index && _simController.selectedType.value == SimObjectType.audio) {
            deselectAllObjects();
            comp.removeAll(comp.children);
          } else {
            //deselect all objects
            deselectAllObjects();
            //then select audio object
            _simController.selectedSimObjectIndex.value = index;
            _simController.selectedType.value = SimObjectType.audio;
            _clipboardController.clearSelection();
            _clipboardController.selectedObjects.refresh();
            _clipboardController.selectedObjects.add(sound);
            _simController.currentSim.refresh();
          }
        },
      )
        ..sprite = Sprite(volumeIcon)
        ..position = Vector2(sound.x, sound.y)
        ..anchor = Anchor.center
        ..size = Vector2(initialSize * sound.widthScale * playModeCompensator.x, initialSize * sound.heightScale * playModeCompensator.y)
        ..opacity = playMode ? 0 : sound.opacity
        ..priority = (sound.priority + 1) *
            (_simController.selectedType.value == SimObjectType.audio && _simController.selectedSimObjectIndex.value == index ? 9000 : 1);

      if (_simController.selectedSimObjectIndex.value == index && _simController.selectedType.value == SimObjectType.audio) {
        final selectedContainer = RectangleComponent(
          position: Vector2(0, 0),
          size: sc.size,
          anchor: Anchor.topLeft,
          paint: highlightPaint,
        );
        final playPauseButton = SimSpriteComponent(
          onDragged: (comp, event) {},
          onTapped: (comp, event) {
            if (audio.state == PlayerState.playing) {
              return audio.stop();
            } else {
              return audio.resume();
            }
          },
        )
          ..sprite = Sprite(playPauseIcon)
          ..size = sc.size * 0.2
          ..position = sc.size / 2
          ..anchor = Anchor.center;

        selectedContainer.add(playPauseButton);
        // sc.add(selectedContainer);
        selectedComponent = sc;
      }

      add(sc);

      if (playMode && !playedAudio.contains(sound.id)) {
        audio.resume();
        playedAudio.add(sound.id);
      }
    });

    await Future.wait(sim.locations[_simController.currentLocation.value].labels.mapIndexed((index, label) async {
      if (label.hidden) return;
      final intendedSize = Vector2(
        initialSize * label.scale * label.widthScale * (playMode ? playModeCompensator.x : 1),
        initialSize * label.scale * label.heightScale * (playMode ? playModeCompensator.y : 1),
      );
      final comp = await buildLabelFromType(label.type, variables: label.variables, size: intendedSize);
      // final lc = comp;
      // lc
      //   ..position = Vector2(label.x * size.x / sim.width, label.y * size.y / sim.height)
      //   ..size = Vector2(initialSize * label.widthScale, initialSize * label.heightScale)
      //   ..angle = label.rotation * pi / 180
      //   ..anchor = Anchor.center
      //   ..scale = Vector2(label.scale * label.widthScale, label.scale * label.heightScale);
      final lc = SimShapeComponent(
        onDragged: (comp, _) {
          if (playMode) return;
          sim.locations[_simController.currentLocation.value].labels[index].x = comp.x;
          sim.locations[_simController.currentLocation.value].labels[index].y = comp.y;
          (findByKeyName("controls") as PositionComponent?)?.position = comp.position - Vector2(comp.width / 2, comp.height / 2);
          _simController.currentSim.refresh();
        },
        onTapped: (comp, _) {
          if (playMode) {
            return;
          }
          if (_simController.newMask.value != null) return;
          if (_simController.selectedType.value == SimObjectType.label && _simController.selectedSimObjectIndex.value == index) {
            deselectAllObjects();
          } else {
            //deselect all objects
            deselectAllObjects();
            //then select label object
            _simController.selectedSimObjectIndex.value = index;
            _simController.selectedType.value = SimObjectType.label;
            _clipboardController.clearSelection();
            _clipboardController.selectedObjects.refresh();
            _clipboardController.selectedObjects.add(label);
          }
          _simController.currentSim.refresh();
        },
        // shape: PolygonComponent.relative([Vector2(1.0, 0.0), Vector2(0.0, -1.0), Vector2(-1.0, 0.0), Vector2(0.0, 1.0)], parentSize: intendedSize),
        // TODO: shape is bound to diamond shapes. there are at least 1 non-diamond shaped labels
        shape: buildUnderlyingShape(label.type, intendedSize)..setColor(Colors.white.withOpacity(0)),
      )
        ..position = Vector2(label.x * size.x / sim.width, label.y * size.y / sim.height)
        ..decorator.addLast(frendering.PaintDecorator.blur(label.blur))
        ..decorator.addLast(frendering.PaintDecorator.tint(label.filterColor))
        // ..size = Vector2(initialSize * label.widthScale, initialSize * label.heightScale)
        ..size = intendedSize
        ..angle = label.rotation * pi / 180
        ..anchor = Anchor.center
        // ..scale = Vector2(label.scale * label.widthScale, label.scale * label.heightScale)
        ..masks = masks.where((mask) => label.maskIds.contains(mask.id)).toList()
        ..priority = (label.priority + 1) *
            (_simController.selectedType.value == SimObjectType.label && _simController.selectedSimObjectIndex.value == index ? 9000 : 1);
      lc.add(
        comp
          ..size = intendedSize
          ..anchor = Anchor.topLeft,
      );
      /* final lc = SimLabelComponent(
        playMode: playMode && !label.movable,
        onDragged: (comp) {
          if (playMode) return;
          // comp.position += event.delta;
        },
        onTapped: (comp) {
          if (playMode) {
            print("Label tapped in play mode");
            return;
          }
          if (_simController.newMask.value != null) return;
          if (_simController.selectedType.value == SimObjectType.label && _simController.selectedSimObjectIndex.value == index) {
            _simController.selectedSimObjectIndex.value = -1;
            _simController.selectedType.value = null;
            _clipboardController.clearSelection();
            _clipboardController.selectedObjects.refresh();
          } else {
            _simController.selectedSimObjectIndex.value = index;
            _simController.selectedType.value = SimObjectType.label;
            _clipboardController.clearSelection();
            _clipboardController.selectedObjects.refresh();
            _clipboardController.selectedObjects.add(label);
          }
          _simController.currentSim.refresh();
        },
        onDragComplete: (p0, p1) {
          if (playMode) return;
          sim.locations[_simController.currentLocation.value].labels[index].x = p0.x;
          sim.locations[_simController.currentLocation.value].labels[index].y = p0.y;
        },
        comp: comp,
        // initSize: Vector2(initialSize * label.widthScale, initialSize * label.heightScale),
      )
        ..position = Vector2(label.x * size.x / sim.width, label.y * size.y / sim.height)
        ..size = Vector2(initialSize * label.widthScale, initialSize * label.heightScale)
        ..angle = label.rotation * pi / 180
        ..anchor = Anchor.center
        ..scale = Vector2(label.scale * label.widthScale, label.scale * label.heightScale)
        ..priority = (label.priority + 1) *
            (_simController.selectedType.value == SimObjectType.label && _simController.selectedSimObjectIndex.value == index ? 9000 : 1); */

      if (_simController.selectedSimObjectIndex.value == index && _simController.selectedType.value == SimObjectType.label) {
        /* final selectedContainer = RectangleComponent(
          position: Vector2(0, 0),
          size: lc.size,
          anchor: Anchor.topLeft,
          paint: highlightPaint,
        );
        final widenComponent = buildWidenComponent(lc, label, evenScale: true);
        final rotationComponent = buildRotationComponent(lc, label);
        final removeComponent = buildRemoveComponent(lc, label);
        selectedContainer.addAll([widenComponent, rotationComponent, removeComponent]); */
        // lc.add(selectedContainer);
        selectedComponent = lc;
      }

      add(lc);
    }));

    sim.locations[_simController.currentLocation.value].containers.forEachIndexed((index, container) {
      if (container.hidden) return;
      // resize container based on image size
      // NOTE: 0.2 should not be ever changed because it will break the scale.
      // Otherwise, we should find a ratio between the initial size and the canvas size and use this as a multiplier
      final initialSize = containersImages["${container.type}-${container.view}"]!.size * (this.initialSize * 0.003);
      final cc = SimSpriteComponent(
        playMode: playMode && !container.movable,
        onDragComplete: (p0) {
          if (playMode) return;
          sim.locations[_simController.currentLocation.value].containers[index].x = p0.x;
          sim.locations[_simController.currentLocation.value].containers[index].y = p0.y;
        },
        onDragged: (comp, event) {
          if (playMode && !container.movable) return;
          comp.position += event.delta;
          // sim.locations[_simController.currentLocation.value].containers[index].x = comp.x;
          // sim.locations[_simController.currentLocation.value].containers[index].y = comp.y;
          (findByKeyName("controls") as PositionComponent?)?.position += event.delta;
        },
        onTapped: (comp, event) {
          print("Label tapped");
          if (playMode) {
            print("Label tapped in play mode");
            return;
          }
          if (_simController.newMask.value != null) return;
          if (_simController.selectedType.value == SimObjectType.container && _simController.selectedSimObjectIndex.value == index) {
            deselectAllObjects();
          } else {
            //deselect all objects
            deselectAllObjects();
            //then select text object
            _simController.selectedSimObjectIndex.value = index;
            _simController.selectedType.value = SimObjectType.container;
            _clipboardController.clearSelection();
            _clipboardController.selectedObjects.refresh();
            _clipboardController.selectedObjects.add(container);
          }
          _simController.currentSim.refresh();
        },
        masks: masks.where((mask) => container.maskIds.contains(mask.id)).toList(),
      )
        ..sprite = Sprite(containersImages["${container.type}-${container.view}"]!)
        ..position = Vector2(container.x * size.x / sim.width, container.y * size.y / sim.height)
        ..size = Vector2(initialSize.x * container.widthScale * container.scale * playModeCompensator.x,
            initialSize.y * container.heightScale * container.scale * playModeCompensator.y)
        // ..scale = Vector2(container.mirrorY ? -1 : 1, container.mirrorX ? -1 : 1)
        ..scale = Vector2(1, 1) // NOTE: keep this as 1, 1. It has to be there or the image won't be flipped
        //                         (probably scale is null and it's not flipping when multiplying null by -1)
        ..angle = container.rotation * pi / 180
        ..anchor = Anchor.center
        ..opacity = hiddenImages.contains(container.id) ? 0 : container.opacity
        ..decorator.addLast(frendering.PaintDecorator.blur(container.blur))
        ..decorator.addLast(frendering.PaintDecorator.tint(container.filterColor))
        ..priority = (container.priority + 1) *
            (_simController.selectedType.value == SimObjectType.container && _simController.selectedSimObjectIndex.value == index ? 9000 : 1);

      if (container.mirrorX) {
        cc.flipVertically();
      }
      if (container.mirrorY) {
        cc.flipHorizontally();
      }

      if (_simController.selectedSimObjectIndex.value == index && _simController.selectedType.value == SimObjectType.container) {
        /* final selectedContainer = RectangleComponent(
          position: Vector2(0, 0),
          size: cc.size,
          anchor: Anchor.topLeft,
          paint: highlightPaint,
        );
        final widenComponent = buildWidenComponent(cc, container, evenScale: true);
        final rotationComponent = buildRotationComponent(cc, container);
        final removeComponent = buildRemoveComponent(cc, container);
        selectedContainer.addAll([widenComponent, rotationComponent, removeComponent]); */
        // cc.add(selectedContainer);
        selectedComponent = cc;
      }

      add(cc);
    });

    sim.locations[_simController.currentLocation.value].timers.forEachIndexed((index, timer) {
      if (timer.hidden) return;
      final tc = SimTextComponent(
        playMode: playMode && !timer.movable,
        onDragged: (comp) {
          if (playMode && !timer.movable) return;
          sim.locations[_simController.currentLocation.value].timers[index].x = comp.x;
          sim.locations[_simController.currentLocation.value].timers[index].y = comp.y;
          (findByKeyName("controls") as PositionComponent?)?.position = comp.position - Vector2(comp.width / 2, comp.height / 2);
        },
        onTapped: (comp) {
          if (playMode) return;
          if (_simController.newMask.value != null) return;
          if (_simController.selectedType.value == SimObjectType.timer && _simController.selectedSimObjectIndex.value == index) {
            deselectAllObjects();
          } else {
            //deselect all objects
            deselectAllObjects();
            //then select text object
            _simController.selectedType.value = SimObjectType.timer;
            _simController.selectedSimObjectIndex.value = index;
            _clipboardController.clearSelection();
            _clipboardController.selectedObjects.refresh();
            _clipboardController.selectedObjects.add(timer);
          }
          _simController.currentSim.refresh();
        },
        // cKey: timer.id,
        cKey: "timer-$index",
      )
        ..text = playMode ? "" : (timer.format == SimTimerFormat.hourMinuteSeconds ? "HH:MM:SS" : "MM:SS")
        ..position = Vector2(timer.x * size.x / sim.width, timer.y * size.y / sim.height)
        ..size = Vector2(12 * timer.scale * 8 * timer.widthScale * playModeCompensator.x,
            35 * timer.scale * timer.heightScale * playModeCompensator.y) // Always use 8 characters as a width scale
        ..angle = timer.rotation * pi / 180
        ..anchor = Anchor.center
        ..scale = Vector2(timer.widthScale * timer.scale, timer.heightScale * timer.scale)
        ..priority = (timer.priority + 1) *
            (_simController.selectedType.value == SimObjectType.timer && _simController.selectedSimObjectIndex.value == index ? 9000 : 1)
        ..textRenderer = TextPaint(
          style: TextStyle(
            color: timer.filterColor,
            fontSize: 24,
          ),
        );
      if (_simController.selectedType.value == SimObjectType.timer && _simController.selectedSimObjectIndex.value == index) {
        /* final selectedContainer = RectangleComponent(
          position: Vector2(0, 0),
          size: tc.size,
          anchor: Anchor.topLeft,
          paint: highlightPaint,
        );
        final widenComponent = buildWidenComponent(tc, timer);
        final rotationComponent = buildRotationComponent(tc, timer, multiper: Vector2(3, 1));
        final removeComponent = buildRemoveComponent(tc, timer);
        selectedContainer.addAll([widenComponent, rotationComponent, removeComponent]); */
        // tc.add(selectedContainer);
        selectedComponent = tc;
      }
      if (playMode) {
        if (timer.fadeInWhen > 0 && timer.trigger.isNotEmpty) {
          bool shouldCreateFadeIn = true;
          switch (timer.trigger) {
            case "scenario":
              if (simTime > timer.fadeInWhen) {
                print("Sim time larger than fade in time");
                tc.textRenderer = TextPaint(
                  style: TextStyle(
                    color: timer.filterColor.withOpacity(1),
                    fontSize: 24,
                  ),
                );
                shouldCreateFadeIn = false;
              }
              break;
            case "state":
              if (stateTime > timer.fadeInWhen) {
                print("State time larger than fade in time");
                tc.textRenderer = TextPaint(
                  style: TextStyle(
                    color: timer.filterColor.withOpacity(1),
                    fontSize: 24,
                  ),
                );
                shouldCreateFadeIn = false;
              }
              break;
            case "location":
              if (locationTime > timer.fadeInWhen) {
                print("Location time larger than fade in time");
                tc.textRenderer = TextPaint(
                  style: TextStyle(
                    color: timer.filterColor.withOpacity(1),
                    fontSize: 24,
                  ),
                );
                shouldCreateFadeIn = false;
              }
              break;
            default:
              print("Unknown trigger: ${timer.trigger}");
              return;
          }
          print("""
          sim time: $simTime, statetime: $stateTime, locationtime: $locationTime,
          shape fade in when: ${timer.fadeInWhen}, shape trigger: ${timer.trigger}
          """);
          if (shouldCreateFadeIn) {
            if (!timer.triggerOnce || !itemsTriggeredOnce["timers"]!["fadeIn"]!.contains(timer.id)) {
              createTimerFadeIn(timer, tc).then(((_) {
                itemsTriggeredOnce["timers"]!["fadeIn"]!.add(timer.id);
              }));
            }
          }
        }
        if (timer.fadeOutWhen > 0 && timer.trigger.isNotEmpty) {
          bool shouldCreateFadeOut = true;
          final totalTime = timer.fadeOutWhen + timer.fadeInWhen;
          switch (timer.trigger) {
            case "scenario":
              if (simTime > totalTime) {
                tc.textRenderer = TextPaint(
                  style: TextStyle(
                    color: timer.filterColor.withOpacity(0),
                    fontSize: 24,
                  ),
                );
                shouldCreateFadeOut = false;
              }
              break;
            case "state":
              if (stateTime > totalTime) {
                tc.textRenderer = TextPaint(
                  style: TextStyle(
                    color: timer.filterColor.withOpacity(0),
                    fontSize: 24,
                  ),
                );
                shouldCreateFadeOut = false;
              }
              break;
            case "location":
              if (locationTime > totalTime) {
                tc.textRenderer = TextPaint(
                  style: TextStyle(
                    color: timer.filterColor.withOpacity(0),
                    fontSize: 24,
                  ),
                );
                shouldCreateFadeOut = false;
              }
              break;
            default:
              print("Unknown trigger: ${timer.trigger}");
              return;
          }
          if (shouldCreateFadeOut) {
            if (!timer.triggerOnce || !itemsTriggeredOnce["timers"]!["fadeOut"]!.contains(timer.id)) {
              createTimerFadeOut(timer, tc).then((_) {
                itemsTriggeredOnce["timers"]!["fadeOut"]!.add(timer.id);
              });
            }
          }
        }
      }
      add(tc);
    });

    // final locationState = sim.locations[_simController.currentLocation.value];
    // final locationObjects = [...locationState.sprites, ...locationState.shapes, ...locationState.images];
    masks.where((mask) {
      return (mask.locationId == sim.locations[_simController.currentLocation.value].id);
      /* for (var obj in locationObjects) {
        if (obj.maskIds.contains(mask.id)) return true;
      }
      return false; */
    }).forEachIndexed((index, mask) {
      if (playMode) return;
      // final maskColor = playMode ? Colors.transparent : Color(int.tryParse(mask.color, radix: 16) ?? 0xff000000).withOpacity(0.75);
      final maskColor = Color(int.tryParse(mask.color, radix: 16) ?? 0xff000000).withOpacity(maskColorOpacity);
      final localShapePaint = BasicPalette.white.paint()..style = PaintingStyle.fill;
      double minX = double.infinity;
      double minY = double.infinity;
      double maxX = 0;
      double maxY = 0;
      for (var coor in mask.coordinates) {
        if (coor.x < minX) {
          minX = coor.x;
        }
        if (coor.x > maxX) {
          maxX = coor.x;
        }
        if (coor.y < minY) {
          minY = coor.y;
        }
        if (coor.y > maxY) {
          maxY = coor.y;
        }
      }
      final maskSize = Vector2(maxX - minX, maxY - minY);
      // print("Mask Size: $maskSize ___ ($maxX - $minX) ($maxY - $minY)");
      final maskComp = SimShapeComponent(
        playMode: playMode,
        isMask: true,
        onDragged: ((comp, _) {
          if (playMode) return;
          final currentMaskIndex = sim.masks.indexWhere((m) => m.id == mask.id);
          final currentMask = sim.masks[currentMaskIndex];
          // Fetch the leftmost and topmost points and use those as the origin for finding the diff. These can be from 2 different coordinates.
          final diff = comp.position -
              Vector2(
                currentMask.coordinates.sortedByCompare((val) => val.x, (double a, double b) => (a - b).toInt())[0].x,
                currentMask.coordinates.sortedByCompare((val) => val.y, (double a, double b) => (a - b).toInt())[0].y,
              );
          // print("Diff: $diff (${diff.x}, ${diff.y})  -- ${comp.position} - ${Vector2(currentMask.coordinates[0].x, currentMask.coordinates[0].y)}");
          print("Diff: $diff (${diff.x}, ${diff.y})  -- ${comp.position} - ${Vector2(currentMask.coordinates[0].x, currentMask.coordinates[0].y)}");
          // if (diff.x < 1 && diff.x > -1) {
          //   diff.x = 0;
          // }
          // if (diff.y < 1 && diff.y > -1) {
          //   diff.y = 0;
          // }
          final updated = currentMask.coordinates.map((coor) => Coordinate(coor.x + diff.x, coor.y + diff.y)).toList();
          /* bool tooClose = false;
          // check every coordinate with the one after it and check if the difference between them is less than 0.5
          for (var i = 0; i < updated.length - 1; i++) {
            final coor = updated[i];
            final nextCoor = updated[i + 1];
            if ((nextCoor.x - coor.x).abs() < 0.5 || (nextCoor.y - coor.y).abs() < 0.5) {
              tooClose = true;
              break;
            }
          }
          print("Updated mask coordinates: $updated");
          if (tooClose) {
            print("Too close. Not updating");
            return;
          } */

          currentMask.coordinates = updated;
          sim.masks[currentMaskIndex] = currentMask;
          redraw();
        }),
        onTapped: (comp, _) {
          if (playMode) return;
          if (_simController.selectedSimObjectIndex.value == index && _simController.selectedType.value == SimObjectType.mask) {
            _simController.selectedSimObjectIndex.value = -1;
            _simController.selectedType.value = null;
          } else {
            _simController.selectedSimObjectIndex.value = index;
            _simController.selectedType.value = SimObjectType.mask;
          }
          redraw();
        },
      )
        ..shape = PolygonComponent(
          mask.coordinates.map((coor) => Vector2(coor.x, coor.y)).toList(),
          paint: localShapePaint
            ..color = maskColor.withOpacity(maskColorOpacity)
            ..strokeWidth = 3.5
            ..style = mask.type == MaskType.showWithin ? PaintingStyle.stroke : PaintingStyle.fill,
          // shrinkToBounds: true,
          children: _simController.selectedSimObjectIndex.value == index && _simController.selectedType.value == SimObjectType.mask
              ? [
                  PolygonComponent(mask.coordinates.map((coor) => Vector2(coor.x, coor.y)).toList())
                    ..position -= playMode
                        ? Vector2(
                            mask.coordinates[0].x * size.x /* (Get.width / sim.width) */,
                            mask.coordinates[0].y * size.y /* (Get.height / sim.height) */,
                          )
                        : Vector2(mask.coordinates[0].x, mask.coordinates[0].y)
                  // ..position -= Vector2(
                  // mask.coordinates[0].x * (size.x / sim.width),
                  // mask.coordinates[0].y * (size.y / sim.height),
                  // )
                  // ..size = maskSize
                  // ..size = size
                  // ..size = Vector2.all(100)
                ]
              : [],
        )
        ..position = Vector2(minX, minY)
        ..anchor = Anchor.topLeft
        ..size = maskSize
        ..priority = 500;
      // ..size = size;

      if (_simController.selectedSimObjectIndex.value == index && _simController.selectedType.value == SimObjectType.mask) {
        // final selectedContainer = RectangleComponent(
        //   position: Vector2(0, 0),
        //   size: maskComp.size,
        //   anchor: Anchor.topLeft,
        //   paint: highlightPaint,
        // );
        final selectedContainer = PolygonComponent(
          mask.coordinates.map((coor) => Vector2(coor.x - minX, coor.y - minY)).toList(),
          size: maskComp.size,
          anchor: Anchor.topLeft,
          paint: highlightPaint,
        );
        maskComp.add(selectedContainer);
      }
      add(maskComp);
    });

    // selected component
    // NOTE: the check on the index is to prevent writing a deselect component code in every object type
    if (!playMode &&
        selectedComponent != null &&
        _simController.selectedType.value != SimObjectType.mask &&
        _simController.selectedSimObjectIndex.value != -1) {
      drawControls();
    }
  }

  void deselectAllObjects() {
    _simController.selectedType.value = null;
    _simController.selectedSimObjectIndex.value = -1;
    _clipboardController.clearSelection();
    _clipboardController.selectedObjects.refresh();
  }

  /* @override
  void update(double dt) {
    print("Delta Time: $dt");
    print("Sprites: $sprites");
  } */
}

findCentroid(List<Offset> coords) {
  double x = 0;
  double y = 0;
  for (var coor in coords) {
    x += coor.dx;
    y += coor.dy;
  }
  return Offset(x / coords.length, y / coords.length);
}

Offset rotateOffset(Offset input, Offset center, double angle) {
  final double x = input.dx;
  final double y = input.dy;
  final double rx0 = center.dx;
  final double ry0 = center.dy;
  final double x0 = (x - rx0) * cos(angle) - (y - ry0) * sin(angle) + rx0;
  final double y0 = (x - rx0) * sin(angle) + (y - ry0) * cos(angle) + ry0;
  return Offset(x0, y0);
}

class SimSpriteComponent extends SpriteComponent with DragCallbacks, TapCallbacks {
  final Function(SimSpriteComponent, DragUpdateEvent) onDragged;
  final Function(SimSpriteComponent, TapUpEvent) onTapped;
  Function()? onDragStarted;
  Function(SimSpriteComponent)? onDragComplete;
  Function(SimSpriteComponent, TapUpEvent)? onDoubleTap;
  final bool playMode;
  List<Mask> masks;
  DateTime lastTap = DateTime.now();
  static const doubleTapThreshold = 200;
  bool printed = false;

  SimSpriteComponent(
      {required this.onDragged,
      required this.onTapped,
      this.onDragStarted,
      this.onDragComplete,
      this.onDoubleTap,
      this.playMode = false,
      this.masks = const []})
      : super();

  @override
  void render(canvas) {
    if (masks.isEmpty) {
      try {
        return super.render(canvas);
      } catch (e, s) {
        return print("Error rendering sprite: $e --> $s");
      }
      // return super.render(canvas);
    }
    canvas.clipRect(Rect.fromLTWH(0, 0, width * scale.x.abs(), height * scale.y.abs()));
    // NOTE: compensator subtracts haf the size because the anchor is set to the center of the sprite
    final compensatorX = position.x - (size.x / 2);
    final compensatorY = position.y - (size.y / 2);

    if (masks.isNotEmpty) {
      masks.where((mask) => mask.type == MaskType.showWithin).forEach((mask) {
        final path = ui.Path();
        List<Offset> coords = mask.coordinates.map((coor) => Offset(coor.x - compensatorX, coor.y - compensatorY)).toList();
        if (angle != 0.0) {
          coords = rotateOffsetAboutPoint(coords, Offset(size.x / 2, size.y / 2), -angle);
        }
        // final Offset centroid = findCentroid(coords);
        // final rotatedCoords = coords.map((coor) => rotateOffset(coor, centroid, angle)).toList();
        // path.addPolygon(rotatedCoords, true);
        path.addPolygon(coords, true);
        canvas.clipPath(path);
      });
    }
    super.render(canvas);
    if (masks.isNotEmpty) {
      masks.where((mask) => mask.type == MaskType.showOutside).forEach((mask) {
        final path = ui.Path();
        List<Offset> coords = mask.coordinates.map((coor) => Offset(coor.x - compensatorX, coor.y - compensatorY)).toList();
        if (angle != 0.0) {
          coords = rotateOffsetAboutPoint(coords, Offset(size.x / 2, size.y / 2), -angle);
        }
        // print("Coords: $coords");
        // final Offset centroid = findCentroid(coords);
        // print("Centroid: $centroid");
        // final rotatedCoords = coords.map((coor) => rotateOffset(coor, centroid, angle)).toList();
        // print("Rotated Coords: $rotatedCoords");
        // path.addPolygon(rotatedCoords, true);
        path.addPolygon(coords, true);
        final clearPaint = BasicPalette.transparent.paint()..blendMode = BlendMode.clear;
        canvas.drawPath(
          path,
          clearPaint,
        );
      });
    }
  }

  @override
  void onTapUp(TapUpEvent event) {
    super.onTapUp(event);
    if (onDoubleTap != null && DateTime.now().difference(lastTap).inMilliseconds <= doubleTapThreshold) {
      onDoubleTap!(this, event);
    } else {
      onTapped(this, event);
    }
    lastTap = DateTime.now();
  }

  @override
  void onDragUpdate(DragUpdateEvent event) {
    if (playMode) return;
    super.onDragUpdate(event);
    onDragged(this, event);
  }

  @override
  void onDragStart(DragStartEvent event) {
    super.onDragStart(event);
    if (onDragStarted != null) {
      onDragStarted!();
    }
  }

  @override
  void onDragEnd(DragEndEvent event) {
    super.onDragEnd(event);
    if (onDragComplete != null) {
      onDragComplete!(this);
    }
    Get.find<SimController>().saved.value = false;
  }
}

class SimSpriteAnimationComponent extends SpriteAnimationComponent with DragCallbacks, TapCallbacks {
  late final String id;
  final Function(SimSpriteAnimationComponent) onDragged;
  final Function(SimSpriteAnimationComponent) onTapped;
  final Vector2 simSize;
  List<Mask> masks;
  final bool playMode;
  double rotation = 0.0;

  bool selected = false;

  SimSpriteAnimationComponent(
      {required this.onDragged, required this.onTapped, required this.simSize, this.masks = const [], this.playMode = false, this.rotation = 0.0})
      : super() {
    id = nanoid(14);
  }

  @override
  void render(ui.Canvas canvas) {
    /* final maskCompensatorX = position.x + (size.x / 2);
    final maskCompensatorY = position.y + (size.y / 2);
    print(
        "Masks before: $masks & rotation: $rotation & position: $position & compensators ($maskCompensatorX, $maskCompensatorY) & given size is ($width, $height)");
    masks = masks
        .map((mask) => mask.rotateAboutPoint(Coordinate(position.x - maskCompensatorX, position.y - maskCompensatorY), 1 * rotation / pi / 180))
        // .map((mask) => mask.rotateAboutPoint(Coordinate(simSize.x / 2, simSize.y / 2), -1 * rotation))
        .toList();
    print("Masks after: $masks"); */
    /*  
      TODO: VIP: this should be replaced with a series of nested or one combined ClipComponent.polygon instance outside the render function.
      Might not work actually because we have flippable masks
    */
    canvas.clipRect(Rect.fromLTWH(0, 0, width * scale.x.abs(), height * scale.y.abs()));
    // rotation can be handled with canvas.rotate but it breaks the rotation
    // canvas.rotate(-angle);
    // super.render(canvas);
    // TODO: compensator needs to fixed to handle different ratios
    // NOTE: compensator subtracts half the size because the anchor is set to the center of the sprite
    final compensatorX = position.x - (size.x / 2);
    final compensatorY = position.y - (size.y / 2);

    if (masks.isNotEmpty) {
      masks.where((mask) => mask.type == MaskType.showWithin).forEach((mask) {
        final path = ui.Path();
        List<Offset> coords = mask.coordinates.map((coor) => Offset(coor.x - compensatorX, coor.y - compensatorY)).toList();
        if (angle != 0.0) {
          coords = rotateOffsetAboutPoint(coords, Offset(size.x / 2, size.y / 2), -angle);
        }
        // List<Offset> coords = mask.coordinates.map((coor) => Offset(coor.x, coor.y)).toList();
        /* coords = coords
            .map((coor) => Offset(coor.dx * cos(rotation) - coor.dy * sin(rotation), coor.dy * cos(rotation) + coor.dx * sin(rotation)))
            .toList(); */
        path.addPolygon(coords, true);
        // path.addPolygon(
        //     mask.coordinates
        //         .map((coor) => Offset((coor.x - compensatorX) * (size.x / simSize.x), (coor.y - compensatorY) * (size.y / simSize.y)))
        //         .toList(),
        //     true);
        // path.addPolygon(mask.coordinates.map((coor) => Offset(coor.x * (size.x / simSize.x), coor.y * (size.y / simSize.y))).toList(), true);
        // canvas.renderRotated(angle, Vector2(position.x, position.y), (canvas) {
        //   canvas.clipPath(path);
        // });
        canvas.clipPath(path);
      });
    }
    // animation?.getSprite().render(canvas, size: size);
    super.render(canvas);
    if (masks.isNotEmpty) {
      masks.where((mask) => mask.type == MaskType.showOutside).forEach((mask) {
        final path = ui.Path();
        List<Offset> coords = mask.coordinates.map((coor) => Offset(coor.x - compensatorX, coor.y - compensatorY)).toList();
        if (angle != 0.0) {
          coords = rotateOffsetAboutPoint(coords, Offset(size.x / 2, size.y / 2), -angle);
        }
        path.addPolygon(coords, true);
        // path.addPolygon(mask.coordinates.map((coor) => Offset(coor.x, coor.y)).toList(), true);
        // path.addPolygon(
        //     mask.coordinates
        //         .map((coor) => Offset((coor.x - compensatorX) * (size.x / simSize.x), (coor.y - compensatorY) * (size.y / simSize.y)))
        //         .toList(),
        //     true);
        // path.addPolygon(mask.coordinates.map((coor) => Offset(coor.x * (size.x / simSize.x), coor.y * (size.y / simSize.y))).toList(), true);
        final clearPaint = BasicPalette.transparent.paint()..blendMode = BlendMode.clear;
        // canvas.renderRotated(-angle, simSize / 2, (canvas) {
        //   canvas.drawPath(
        //     path,
        //     clearPaint,
        //   );
        // });
        canvas.drawPath(
          path,
          clearPaint,
        );
      });
    }
    // super.render(canvas);
    // animation?.getSprite().render(canvas, size: size);
  }

  @override
  void onTapUp(TapUpEvent event) {
    super.onTapUp(event);
    selected = !selected;
    onTapped(this);
  }

  @override
  void onDragUpdate(DragUpdateEvent event) {
    if (playMode) return;
    super.onDragUpdate(event);
    // print("Local Position: ${event.localPosition}, Canvas Position: ${event.canvasPosition}, Device Position: ${event.devicePosition}");
    position += event.delta;
    // for (var child in children) {
    //   child.positionType.
    // }
  }

  @override
  void onDragEnd(DragEndEvent event) {
    super.onDragEnd(event);
    print("Absolute Position: $absolutePosition");
    onDragged(this);
    Get.find<SimController>().saved.value = false;
  }
}

class SimTextComponent extends TextComponent with DragCallbacks, TapCallbacks {
  final Function(SimTextComponent) onDragged;
  final Function(SimTextComponent) onTapped;
  final Function(SimTextComponent)? onDoubleTap;
  final bool playMode;
  final String? cKey;
  SimTextComponent({required this.onDragged, required this.onTapped, this.playMode = false, this.onDoubleTap, this.cKey})
      : super(key: cKey != null ? ComponentKey.named(cKey) : null);

  DateTime? lastTapTime;

  @override
  void onDragUpdate(DragUpdateEvent event) {
    if (playMode) return;
    super.onDragUpdate(event);
    position += event.delta;
  }

  @override
  void onDragEnd(DragEndEvent event) {
    super.onDragEnd(event);
    onDragged(this);
    Get.find<SimController>().saved.value = false;
  }

  @override
  void onTapUp(TapUpEvent event) {
    if (lastTapTime != null && DateTime.now().difference(lastTapTime!).inMilliseconds < 300) {
      print("Time difference: ${DateTime.now().difference(lastTapTime!).inMilliseconds}");
      doubleTapHanlder(event);
      lastTapTime = null;
      return;
    }
    Future.delayed(const Duration(milliseconds: 300), () {
      if (lastTapTime == null) return;
      super.onTapUp(event);
      onTapped(this);
      lastTapTime = null;
    });
    lastTapTime = DateTime.now();
  }

  doubleTapHanlder(TapUpEvent event) {
    print("Double Tapped");
    if (onDoubleTap != null) {
      onDoubleTap!(this);
    }
  }
}

class SimShapeComponent extends PositionComponent with DragCallbacks, TapCallbacks {
  final Function(SimShapeComponent comp, DragEndEvent event) onDragged;
  final Function(SimShapeComponent comp, TapUpEvent event) onTapped;
  ShapeComponent? shape;
  SimShape? shapeObject;
  final bool isMask;
  List<Mask> masks;
  final bool playMode;
  SimShapeComponent(
      {required this.onDragged,
      required this.onTapped,
      this.shapeObject,
      this.shape,
      this.isMask = false,
      this.masks = const [],
      this.playMode = false})
      : super();

  // bool printOnce = false;

  clipToShape(ui.Canvas canvas) {
    if (shapeObject == null || masks.isEmpty) {
      canvas.clipRect(Rect.fromLTWH(0, 0, width * scale.x.abs(), height * scale.y.abs()));
      return;
    }
    // canvas.clipPath(path)
    switch (shapeObject!.shape) {
      case "square":
      case "4":
        canvas.clipRect(Rect.fromLTWH(0, 0, size.x, size.y));
        break;
      case "rectangle":
      case "5":
        canvas.clipRect(Rect.fromLTWH(0, 0, size.x, size.y));
        break;
      case "rounded-rectangle":
        canvas.clipRRect(RoundedRectangle.fromLTRBR(0, 0, size.x, size.y, 10).asRRect());
        break;
      case "circle":
      case "6":
        canvas.clipRRect(ui.RRect.fromRectAndRadius(Rect.fromLTWH(0, 0, size.x, size.y), ui.Radius.circular(size.x / 2)));
        break;
      case "triangle":
      case "7":
        canvas.clipPath(ui.Path()..addPolygon([Offset(0, size.y), Offset(size.x / 2, 0), Offset(size.x, size.y)], true));
        break;
      case "arrow":
      case "0":
      case "1":
      case "2":
      case "3":
        canvas.clipPath(ui.Path()
          ..addPolygon([
            Offset(0, size.y * 0.489),
            Offset(size.x * 0.75, size.y * 0.489),
            Offset(size.x * 0.45, size.y * 0.075),
            Offset(size.x * 0.65, size.y * 0.075),
            Offset(size.x, size.y / 2),
            Offset(size.x * 0.65, size.y * 0.925),
            Offset(size.x * 0.45, size.y * 0.925),
            Offset(size.x * 0.75, size.y * 0.489),
            Offset(0, size.y * 0.489),
          ], true));
        break;
      default:
        canvas.clipRect(Rect.fromLTWH(0, 0, width * scale.x.abs(), height * scale.y.abs()));
        return;
    }
  }

  @override
  void render(ui.Canvas canvas) {
    // if (isMask) {
    //   return super.render(canvas);
    // }
    clipToShape(canvas);

    final compensatorX = position.x - (size.x / 2);
    final compensatorY = position.y - (size.y / 2);
    if (masks.isNotEmpty) {
      masks.where((mask) => mask.type == MaskType.showWithin).forEach((mask) {
        final path = ui.Path();
        List<Offset> coords = mask.coordinates.map((coor) => Offset(coor.x - compensatorX, coor.y - compensatorY)).toList();
        if (angle != 0.0) {
          coords = rotateOffsetAboutPoint(coords, Offset(size.x / 2, size.y / 2), -angle);
        }
        path.addPolygon(coords, true);
        canvas.clipPath(path);
      });
    }
    shape!.onLoad();
    shape!.render(canvas);
    if (masks.isNotEmpty) {
      masks.where((mask) => mask.type == MaskType.showOutside).forEach((mask) {
        final path = ui.Path();
        List<Offset> coords = mask.coordinates.map((coor) => Offset(coor.x - compensatorX, coor.y - compensatorY)).toList();
        if (angle != 0.0) {
          coords = rotateOffsetAboutPoint(coords, Offset(size.x / 2, size.y / 2), -angle);
        }
        path.addPolygon(coords, true);
        final clearPaint = BasicPalette.transparent.paint()..blendMode = BlendMode.clear;
        canvas.drawPath(
          path,
          clearPaint,
        );
      });
    }

    /* if (!printOnce) {
      if (!isMask) {
        print("width: $width, height: $height, scalex: ${scale.x}, scaley: ${scale.y}");
        print("SO: ${width * scale.x.abs()}, ${height * scale.y.abs()}");
        print("BUT shape size: ${shape?.size}");
        printOnce = true;
      }
    } */

    // canvas.drawRect(Rect.fromLTWH(0, 0, width * scale.x.abs(), height * scale.y.abs()), BasicPalette.pink.paint());
    // shape!.render(canvas);
  }

  @override
  void onDragUpdate(DragUpdateEvent event) {
    if (playMode) return;
    super.onDragUpdate(event);
    position += event.delta;
  }

  @override
  void onDragEnd(DragEndEvent event) {
    super.onDragEnd(event);
    onDragged(this, event);
    Get.find<SimController>().saved.value = false;
  }

  @override
  void onTapUp(TapUpEvent event) {
    super.onTapUp(event);
    onTapped(this, event);
  }
}

class LineComponent extends ShapeComponent {
  Vector2 start;
  Vector2 end;
  Color color;

  LineComponent({required this.start, required this.end, this.color = Colors.white}) : super();

  @override
  void render(ui.Canvas canvas) {
    super.render(canvas);
    final paint = BasicPalette.white.paint()
      ..color = color
      ..strokeWidth = 3;
    canvas.drawLine(Offset(start.x, start.y), Offset(end.x, end.y), paint);
  }
}

class SimLabelComponent extends PositionComponent with DragCallbacks, TapCallbacks {
  // final Function(SimLabelComponent, DragUpdateEvent) onDragged;
  // final Function(SimLabelComponent, TapDownEvent) onTapped;
  final Function(SimLabelComponent) onDragged;
  final Function(SimLabelComponent) onTapped;
  Function(SimLabelComponent, DragEndEvent)? onDragComplete;
  final bool playMode;
  final PositionComponent comp;
  SimLabelComponent({
    required this.onDragged,
    required this.onTapped,
    this.onDragComplete,
    required this.comp,
    this.playMode = false,
  }) : super();

  @override
  void render(ui.Canvas canvas) {
    // return super.render(canvas);
    comp.render(canvas);
  }

  @override
  void onDragUpdate(DragUpdateEvent event) {
    if (playMode) return;
    super.onDragUpdate(event);
    position += event.delta;
    onDragged(this);
  }

  @override
  void onDragEnd(DragEndEvent event) {
    super.onDragEnd(event);
    onDragged(this);
    onDragComplete?.call(this, event);
    Get.find<SimController>().saved.value = false;
  }

  @override
  void onTapUp(TapUpEvent event) {
    super.onTapUp(event);
    onTapped(this);
  }
}

class BackgroundColorComponent extends PositionComponent with DragCallbacks, TapCallbacks {
  final Color color;
  final Function(BackgroundColorComponent comp, DragEndEvent event) onDragged;
  final Function(BackgroundColorComponent comp, TapUpEvent event) onTapped;
  BackgroundColorComponent({required this.color, required this.onDragged, required this.onTapped}) : super();

  @override
  void render(ui.Canvas canvas) {
    super.render(canvas);
    canvas.drawRect(Rect.fromLTWH(0, 0, size.x, size.y), ui.Paint()..color = color);
  }

  @override
  void onDragEnd(DragEndEvent event) {
    super.onDragEnd(event);
    onDragged(this, event);
    Get.find<SimController>().saved.value = false;
  }

  @override
  void onTapUp(TapUpEvent event) {
    super.onTapUp(event);
    onTapped(this, event);
  }
}
